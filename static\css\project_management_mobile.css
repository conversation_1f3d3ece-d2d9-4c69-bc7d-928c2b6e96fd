/* 项目管理移动端适配样式 */

@media (max-width: 1023px) {
    .is-desktop {
        display: none !important;
    }
    
    /* 隐藏原有的标题 */
    .project-main-area > h2 {
        display: none !important;
    }
    
    /* 隐藏任务紧急程度标签 */
    .el-tag.el-tag--success.el-tag--small.el-tag--plain {
        display: none !important;
    }
    
    /* 侧边栏隐藏 */
    .sidebar {
        display: none !important;
    }
    
    /* 主内容区域宽度调整 */
    .project-main-area {
        margin-left: 0 !important;
        width: 100% !important;
        padding: 0 2px !important;
        box-sizing: border-box !important;
    }
    
    /* 顶部操作按钮区域调整 */
    .action-buttons {
        flex-direction: column;
        align-items: stretch !important;
        width: 100% !important;
        gap: 10px;
        padding: 10px;
    }
    
    /* 标题居中 */
    h2 {
        text-align: center !important;
        width: 100% !important;
    }
    
    /* 表格调整为卡片式布局 */
    .el-table {
        border: none !important;
        width: 100% !important;
        margin: 0 auto !important;
    }
    
    /* 隐藏表头 */
    .el-table th.el-table__cell {
        display: none !important;
    }
    
    /* 每行作为一个卡片 */
    .el-table tr {
        display: block;
        margin-bottom: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        background-color: #fff;
    }
    
    /* 单元格样式调整 */
    .el-table td.el-table__cell {
        display: flex !important;
        padding: 10px 15px !important;
        border-bottom: 1px solid #f0f0f0;
        text-align: left !important;
    }
    
    /* 最后一个单元格无底部边框 */
    .el-table tr td.el-table__cell:last-child {
        border-bottom: none;
    }
    
    /* 为单元格添加标签 */
    .el-table td.el-table__cell:before {
        content: attr(data-label);
        font-weight: bold;
        width: 40%;
        min-width: 120px;
        color: #606266;
    }
    
    /* 单元格内容样式 */
    .el-table td.el-table__cell .cell {
        width: 60%;
        text-align: right;
    }
    
    /* 任务抽屉调整 - 重新设计移动端样式 */
    .task-drawer {
        width: 100% !important;
        right: -100% !important;
        transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: fixed !important;
        top: 0 !important;
        bottom: 0 !important;
        z-index: 2000 !important;
        display: flex !important;
        flex-direction: column !important;
        background-color: #fff !important;
        height: 100% !important;
    }
    
    .task-drawer.open {
        right: 0 !important;
    }
    
    /* 优化抽屉头部 */
    .drawer-header {
        padding: 16px !important;
        border-bottom: 1px solid #f0f0f0 !important;
        position: relative !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        background: linear-gradient(to right, #f9f9f9, #f3f3f3) !important;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
        border-radius: 0 0 15px 15px !important;
        margin-bottom: 10px !important;
    }
    
    .drawer-title {
        font-size: 16px !important;
        font-weight: bold !important;
        color: #333 !important;
        width: calc(100% - 35px) !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8) !important;
        padding-left: 4px !important;
        position: relative !important;
    }
    
    .drawer-title::before {
        content: "" !important;
        display: inline-block !important;
        width: 4px !important;
        height: 16px !important;
        background-color: #F05123 !important;
        margin-right: 8px !important;
        border-radius: 2px !important;
        vertical-align: middle !important;
        position: relative !important;
        top: -1px !important;
    }
    
    .drawer-close {
        width: 32px !important;
        height: 32px !important;
        line-height: 30px !important;
        text-align: center !important;
        border-radius: 50% !important;
        background-color: rgba(255, 255, 255, 0.9) !important;
        color: #555 !important;
        font-size: 20px !important;
        cursor: pointer !important;
        transition: all 0.3s !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
    }
    
    .drawer-close:active {
        transform: scale(0.92) !important;
        background-color: #e0e0e0 !important;
    }
    
    /* 抽屉内容区域 */
    .drawer-content {
        flex: 1 !important;
        overflow-y: auto !important;
        padding: 15px !important;
        -webkit-overflow-scrolling: touch !important;
        padding-bottom: 200px !important;
    }
    
    /* 任务描述信息优化 */
    .drawer-info .el-descriptions {
        margin-bottom: 15px !important;
    }
    
    .drawer-info .el-descriptions__label {
        font-size: 13px !important;
        padding: 10px !important;
    }
    
    .drawer-info .el-descriptions__content {
        font-size: 14px !important;
        padding: 10px !important;
        word-break: break-word !important;
    }
    
    /* 沟通记录优化 */
    .communication-log {
        margin-top: 10px !important;
    }
    
    .el-divider {
        margin: 15px 0 !important;
    }
    
    .el-timeline {
        padding-left: 10px !important;
    }
    
    .el-timeline-item {
        padding-bottom: 15px !important;
    }
    
    .log-content {
        font-size: 13px !important;
        line-height: 1.5 !important;
        margin-top: 5px !important;
    }
    
    /* 回复框优化 */
    .reply-section {
        border-top: 1px solid #f0f0f0 !important;
        padding: 8px 12px !important;
        background: linear-gradient(to bottom, #f9f9f9, #f5f5f5) !important;
        position: sticky !important;
        bottom: 0 !important;
        overflow: hidden !important;
        border-radius: 15px 15px 0 0 !important;
        box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.06) !important;
    }
    
    .reply-section .el-form {
        display: flex !important;
        flex-direction: column !important;
        margin-bottom: 0 !important;
    }
    
    .rich-editor-content {
        max-height: 80px !important;
        min-height: 40px !important;
        border-radius: 8px !important;
        border: 1px solid #e8e8e8 !important;
        background-color: #fff !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03) !important;
        overflow-y: auto !important;
        transition: min-height 0.3s ease !important;
    }
    
    /* 缩短工具栏高度 */
    .rich-editor-toolbar {
        border-bottom: 1px solid #eaeaea !important;
        background-color: #f9f9f9 !important;
        border-radius: 8px 8px 0 0 !important;
        padding: 2px !important;
    }
    
    .rich-editor-toolbar .ql-toolbar {
        padding: 2px !important;
        border-bottom: none !important;
        display: flex !important;
        flex-wrap: wrap !important;
        justify-content: center !important;
        min-height: 28px !important;
    }
    
    .rich-editor-toolbar .ql-toolbar button {
        margin: 0 1px !important;
        padding: 2px !important;
    }
    
    .rich-editor-container {
        margin-bottom: 8px !important;
        border-radius: 8px !important;
        overflow: hidden !important;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
    }
    
    /* 回复按钮优化 */
    .reply-section .el-form-item:last-child {
        margin-bottom: 0 !important;
        margin-top: 8px !important;
    }
    
    .reply-section .el-button {
        padding: 6px 12px !important;
        font-size: 13px !important;
        height: 32px !important;
    }
    
    .reply-section .el-button.el-button--success {
        background: linear-gradient(135deg, #42b983, #36a972) !important;
        border-color: #36a972 !important;
    }
    
    .reply-section .el-button.el-button--danger {
        background: linear-gradient(135deg, #f56c6c, #e64242) !important;
        border-color: #e64242 !important;
    }
    
    .reply-section .el-button:active {
        transform: scale(0.96) !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    }
    
    .reply-section .el-button .el-icon {
        margin-right: 4px !important;
        font-size: 15px !important;
        vertical-align: middle !important;
    }
    
    /* 优化元素描述列表 */
    .el-descriptions__body {
        background-color: #fff !important;
        border-radius: 10px !important;
        box-shadow: 0 1px 6px rgba(0,0,0,0.05) !important;
        margin-bottom: 15px !important;
    }
    
    .el-descriptions__label {
        background-color: #f9f9f9 !important;
    }
    
    /* 优化沟通记录区样式 */
    .el-timeline-item__tail {
        border-left: 2px solid #e4e7ed !important;
    }
    
    .el-timeline-item__node--primary {
        background-color: #F05123 !important;
    }
    
    .el-timeline-item__content {
        padding-bottom: 15px !important;
    }
    
    /* 优化回复区域和沟通记录的间距 */
    .communication-log {
        margin-bottom: 10px !important;
    }
    
    /* 空状态优化 */
    .el-empty {
        padding: 20px 0 !important;
    }
    
    .el-empty__description {
        margin-top: 10px !important;
        font-size: 14px !important;
        color: #909399 !important;
    }
    
    /* 项目卡片调整 */
    .project-card, .template-card {
        width: 100% !important;
        margin: 0 0 15px 0 !important;
    }
    
    /* 统计图表调整 */
    .stats-chart-row {
        flex-direction: column !important;
    }
    
    .stats-chart-box {
        width: 100% !important;
        margin-bottom: 15px !important;
    }
    
    /* 表单调整 */
    .el-form-item {
        margin-bottom: 15px !important;
        width: 100% !important;
    }
    
    /* 对话框调整 */
    .el-dialog {
        width: 95% !important;
        margin: 10px auto !important;
    }
    
    /* 分页组件调整 */
    .pagination-container {
        padding: 10px !important;
        display: flex !important;
        justify-content: center !important;
        width: 100% !important;
    }
    
    .el-pagination {
        justify-content: center !important;
        flex-wrap: wrap !important;
    }
    
    /* 首页仪表盘调整 */
    .home-dashboard {
        padding: 10px 2px !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .home-header {
        text-align: center !important;
    }
    
    .home-greeting, .home-date {
        width: 100% !important;
        text-align: center !important;
    }
    
    /* 修改统计栏为卡片式布局 */
    .stats-bar {
        flex-direction: column !important;
        align-items: center !important;
        padding: 12px 5px !important;
        gap: 8px !important;
        height: auto !important;
        border-radius: 12px !important;
        background-color: #f5f7fa !important;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
    }
    
    /* 下拉框容器样式优化 */
    .stats-period {
        width: 100% !important;
        display: flex !important;
        justify-content: center !important;
        margin-bottom: 8px !important;
        position: relative !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
        padding-bottom: 8px !important;
    }
    
    /* 优化下拉框样式 */
    .stats-period .el-select {
        width: 80px !important;
    }
    
    .stats-period .el-select .el-input__inner {
        border-radius: 12px !important;
        text-align: center !important;
        font-size: 12px !important;
        height: 28px !important;
        line-height: 28px !important;
        padding: 0 8px !important;
        background-color: rgba(255, 255, 255, 0.6) !important;
        border: 1px solid #e0e0e0 !important;
        color: #606266 !important;
        box-shadow: none !important;
    }
    
    /* 调整下拉箭头样式 */
    .stats-period .el-select .el-input__suffix {
        right: 5px !important;
    }
    
    /* 移除分隔线 */
    .stats-divider {
        display: none !important;
    }
    
    /* 统计项容器 */
    .stats-items-container {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: nowrap !important;
        width: 100% !important;
        justify-content: space-between !important;
        padding: 5px 2px !important;
    }
    
    .stats-item {
        width: 22% !important; /* 每行四个项目 */
        text-align: center !important;
        margin: 0 !important;
        padding: 3px !important;
    }
    
    /* 减小统计项字体大小 */
    .stats-item .stats-value {
        font-size: 15px !important;
        font-weight: bold !important;
    }
    
    .stats-item .stats-label {
        font-size: 11px !important;
        color: #606266 !important;
    }
    
    /* 任务容器布局优化 */
    .home-tasks-container {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
        gap: 12px !important;
        margin: 12px auto 0 !important;
        padding: 0 !important;
    }
    
    /* 任务卡片样式优化 */
    .home-task-section {
        width: 100% !important;
        max-width: 100% !important;
        margin-right: 0 !important;
        margin-left: 0 !important;
        margin-bottom: 0 !important;
        padding: 12px 8px !important;
        box-sizing: border-box !important;
        border-radius: 8px !important;
        background-color: #fff !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    }
    
    .task-section-header {
        text-align: left !important;
        margin-bottom: 10px !important;
        padding-left: 5px !important;
    }
    
    .task-section-title {
        font-size: 16px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
    }
    
    .task-section-title-icon {
        font-weight: bold !important;
        color: #333 !important;
    }
    
    .task-section-tabs {
        display: flex !important;
        justify-content: flex-start !important;
        margin-bottom: 12px !important;
        padding-left: 5px !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }
    
    .task-tab {
        padding: 6px 12px !important;
        font-size: 13px !important;
        border-radius: 15px !important;
        margin-right: 8px !important;
        white-space: nowrap !important;
    }
    
    .task-list {
        max-height: 300px !important;
        overflow-y: auto !important;
        padding: 5px !important;
    }
    
    .task-item {
        padding: 10px !important;
        border-radius: 8px !important;
        margin-bottom: 8px !important;
    }
    
    .task-item-project {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 4px !important;
    }
    
    /* 负责人行对齐调整 */
    .task-item-meta-info {
        margin-left: 0 !important;
        padding-left: 0 !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 8px !important;
        width: 100% !important;
    }
    
    .task-creator, 
    .task-assignees, 
    .task-deadline {
        margin-right: 8px !important;
        white-space: nowrap !important;
    }
    
    /* 项目超链接样式 */
    .task-item-project .el-link {
        pointer-events: none !important;
        cursor: default !important;
        color: #606266 !important;
        text-decoration: none !important;
        font-weight: normal !important;
    }
    
    .task-item-project .el-link:hover {
        color: #606266 !important;
        text-decoration: none !important;
    }
    
    /* 修改项目名称显示样式 */
    .task-item-project .el-link.el-link--primary {
        margin-right: 0 !important;
        padding: 0 !important;
        font-size: 12px !important;
    }
    
    .home-overview-container {
        flex-direction: column !important;
        width: 100% !important;
    }
    
    .home-task-overview, .home-focus-section {
        width: 100% !important;
        margin-right: 0 !important;
        margin-bottom: 15px !important;
    }
    
    .home-chart-box {
        width: 100% !important;
        margin-right: 0 !important;
        margin-bottom: 15px !important;
    }
    
    .chart-header {
        text-align: center !important;
    }
    
    /* 任务统计页面调整 */
    .stats-filter-container {
        padding: 10px !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .stats-filter-row {
        flex-direction: column !important;
        align-items: center !important;
    }
    
    .stats-filter-row .el-col {
        width: 100% !important;
        margin-bottom: 10px !important;
    }
    
    /* 快速任务创建区域调整 */
    .quick-task-input {
        flex-wrap: wrap !important;
        justify-content: flex-start !important;
        width: 100% !important;
        padding: 8px 0 !important;
        margin-bottom: 10px !important;
    }
    
    /* 第一行：输入框占满整行 */
    .quick-task-plus {
        margin-right: 8px !important;
    }
    
    .quick-task-text {
        width: calc(100% - 25px) !important;
        flex: 1 !important;
        margin-right: 0 !important;
        padding: 6px 0 !important;
        border-bottom: 1px dashed #e0e0e0 !important;
    }
    
    /* 第二行：选项按钮 */
    .quick-task-options-row {
        display: flex !important;
        width: 100% !important;
        margin-top: 8px !important;
        padding-left: 25px !important;
        justify-content: flex-start !important;
        flex-wrap: wrap !important;
    }
    
    .quick-task-date, 
    .quick-task-project, 
    .quick-task-urgency, 
    .quick-task-assignee {
        margin-top: 0 !important;
        margin-left: 0 !important;
        margin-right: 10px !important;
        margin-bottom: 5px !important;
    }
    
    /* 创建按钮 */
    .quick-task-create-btn,
    .quick-task-options-row .el-button--primary {
        margin-left: auto !important;
        margin-right: 0 !important;
        background-color: #F05123 !important;
        border-color: #F05123 !important;
        padding: 0 15px !important;
        height: 32px !important;
        font-size: 13px !important;
        font-weight: bold !important;
        border-radius: 16px !important;
        box-shadow: 0 2px 5px rgba(240, 81, 35, 0.3) !important;
    }
    
    /* 优化表单控件在移动端的宽度 */
    .el-select, .el-input, .el-date-picker {
        width: 100% !important;
    }
    
    /* 改善查询区域在移动端的展示 */
    .filter-item-style {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        width: 100% !important;
    }
    
    .filter-item-style > span {
        margin-bottom: 5px !important;
        width: 100% !important;
        text-align: left !important;
    }
    
    .el-button-group {
        display: flex !important;
        justify-content: center !important;
        width: 100% !important;
        margin-top: 10px !important;
    }

    /* 编辑器内容区域行距优化 */
    .rich-editor-content [contenteditable="true"] {
        line-height: 1.15 !important;  /* 进一步减小行高 */
    }

    .rich-editor-content [contenteditable="true"] p {
        margin: 0 !important;
        padding: 1px 0 !important;  /* 减小段落内边距 */
    }

    .rich-editor-content .w-e-text-container {
        line-height: 1.15 !important;  /* 统一容器行高 */
    }

    .rich-editor-content .w-e-scroll {
        padding: 4px 6px !important;  /* 减小滚动区域内边距 */
    }

    .rich-editor-content [data-slate-editor] {
        min-height: 16px !important;  /* 进一步减小最小高度 */
    }

    /* 占位符文本样式优化 */
    .rich-editor-content .w-e-text-placeholder {
        line-height: 1.15 !important;  /* 统一占位符行高 */
        padding: 4px 6px !important;  /* 减小占位符内边距 */
    }

    .mobile-received-filters {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .mobile-received-filters .filter-row {
        display: flex;
        flex-direction: row;
        gap: 8px;
        width: 100%;
        align-items: flex-end;
        margin-bottom: 0;
    }

    .mobile-received-filters .filter-row .mobile-search-form {
        display: flex;
        width: 100%;
        gap: 10px;
        flex-grow: 1;
    }

    .mobile-received-filters .filter-row .mobile-search-form .el-form-item {
        margin-bottom: 0 !important;
        flex-grow: 1;
    }
    
    .mobile-received-filters .filter-row .mobile-search-form .el-form-item:last-child {
        flex-grow: 0;
    }

    .mobile-received-filters .filter-row .filter-item-style {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex-grow: 1;
        width: 100%;
    }

    .mobile-received-filters .filter-label {
        margin-bottom: 4px;
        font-weight: 500;
        font-size: 14px;
        color: #606266;
    }

    .mobile-received-filters .task-type-filter {
        margin: 0;
        padding: 0;
    }

    .mobile-received-filters .task-type-filter .el-button-group {
        display: flex;
        width: 100%;
        gap: 5px;
    }
    
    .mobile-received-filters .task-type-filter .el-button-group .el-button {
        flex-grow: 1;
        margin: 0 !important;
        padding: 8px 12px !important;
        height: 32px !important;
        line-height: 1 !important;
    }

    .mobile-received-filters .task-type-filter .filter-label {
        margin-bottom: 4px;
    }

    .mobile-received-filters .task-type-filter .el-button--default {
        margin: 0 !important;
    }

    /* 移动端页面标题样式 */
    .mobile-page-title {
        padding: 0 0 8px 0;
        margin-bottom: 4px;
        border-bottom: 1px solid #ebeef5;
    }

    .mobile-page-title h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        line-height: 1.4;
    }

    /* 强制表格卡片布局 - 强化移动端适配 */
    .project-main-area table,
    .project-main-area .el-table__body,
    .project-main-area .el-table__header,
    .project-main-area .el-table__body-wrapper,
    .project-main-area .el-table__header-wrapper {
        display: block !important;
        width: 100% !important;
    }

    /* 重新定义行的样式为卡片 */
    .project-main-area .el-table__body tr {
        display: block !important;
        margin: 12px 8px !important;
        background-color: #fff !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
        position: relative !important;
        border: 1px solid #f0f0f0 !important;
        overflow: hidden !important;
    }

    /* 设置单元格样式 */
    .project-main-area .el-table__body td {
        display: flex !important;
        padding: 12px !important;
        text-align: left !important;
        border-bottom: 1px solid #f5f5f5 !important;
        align-items: flex-start !important;
        min-height: 24px !important;
        flex-wrap: wrap !important;
    }

    /* 为单元格添加标签 */
    .project-main-area .el-table__body td:before {
        content: attr(data-label);
        font-size: 13px !important;
        color: #909399 !important;
        font-weight: normal !important;
        margin-bottom: 4px !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }

    /* 单元格内容样式 */
    .project-main-area .el-table__body .cell {
        width: 100% !important;
        text-align: left !important;
        font-size: 14px !important;
        color: #303133 !important;
        line-height: 1.4 !important;
        word-break: break-all !important;
        display: block !important;
    }

    /* 优化链接样式 */
    .project-main-area .el-table__body .cell .el-link {
        font-size: 14px !important;
        display: inline-block !important;
        margin: 2px 0 !important;
    }

    /* 优化标签样式 */
    .project-main-area .el-table__body .cell .el-tag {
        margin: 2px 4px 2px 0 !important;
        font-size: 12px !important;
    }

    /* 优化按钮样式 */
    .project-main-area .el-table__body .cell .el-button {
        margin: 4px 8px 4px 0 !important;
        padding: 6px 12px !important;
        font-size: 13px !important;
    }

    /* 优化时间显示 */
    .project-main-area .el-table__body .cell time {
        color: #909399 !important;
        font-size: 13px !important;
    }

    /* 优化用户名显示 */
    .project-main-area .el-table__body .cell .user-name {
        color: #606266 !important;
        font-weight: 500 !important;
    }

    /* 优化任务状态显示 */
    .project-main-area .el-table__body .cell .task-status {
        display: inline-block !important;
        padding: 2px 8px !important;
        border-radius: 10px !important;
        font-size: 12px !important;
        margin: 2px 0 !important;
    }

    /* 优化任务标题显示 */
    .project-main-area .el-table__body .cell .task-title {
        font-weight: 500 !important;
        color: #303133 !important;
        margin: 2px 0 !important;
        line-height: 1.4 !important;
    }

    /* 移动端任务名称文本省略处理 */
    .task-name-text,
    .overdue-name,
    .completed-name {
        display: block !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
        word-break: break-all !important;
    }

    /* 移动端卡片内的任务名称容器 */
    .project-main-area .el-table__body tr .cell {
        max-width: 100% !important;
        overflow: hidden !important;
    }

    /* 确保任务名称在移动端卡片中正确显示 */
    .project-main-area .el-table__body tr .cell .task-name-text {
        max-width: calc(100vw - 80px) !important; /* 减去卡片边距和内边距 */
        display: inline-block !important;
        vertical-align: top !important;
    }

    /* 针对我的关注页面的特殊处理 */
    .mobile-task-content .task-name-text,
    .mobile-task-content .overdue-name,
    .mobile-task-content .completed-name {
        max-width: calc(100vw - 100px) !important; /* 为复选框和其他元素留出空间 */
        display: inline-block !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* 移动端文本省略通用样式类 */
    .mobile-text-ellipsis {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
        display: inline-block !important;
        vertical-align: top !important;
    }

    /* 移动端任务卡片内容区域优化 */
    .mobile-task-content > div:first-child {
        display: flex !important;
        align-items: center !important;
        width: 100% !important;
        overflow: hidden !important;
    }

    /* 确保复选框不被压缩 */
    .mobile-task-content .task-checkbox {
        flex-shrink: 0 !important;
        margin-right: 8px !important;
    }

    /* 任务名称文本容器 */
    .mobile-task-content .task-name-text,
    .mobile-task-content .overdue-name,
    .mobile-task-content .completed-name {
        flex: 1 !important;
        min-width: 0 !important; /* 允许flex项目缩小到内容宽度以下 */
    }

    /* 移动端任务信息区域图标对齐优化 */
    .mobile-task-info {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 15px !important;
        font-size: 13px !important;
        color: #606266 !important;
        align-items: center !important;
    }

    .mobile-task-assignees,
    .mobile-task-deadline,
    .mobile-task-completed {
        display: flex !important;
        align-items: center !important;
        gap: 4px !important;
        line-height: 1.2 !important;
        font-size: 13px !important;
        color: #606266 !important;
        white-space: nowrap !important;
    }

    /* 优化移动端任务信息图标样式 */
    .mobile-task-info .el-icon {
        font-size: 14px !important;
        line-height: 1 !important;
        vertical-align: middle !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        flex-shrink: 0 !important;
        width: 14px !important;
        height: 14px !important;
    }

    /* 优化展开行样式 */
    .project-main-area .el-table__expanded-cell {
        padding: 12px !important;
        background-color: #fafafa !important;
    }

    /* 优化表格容器样式 */
    .project-main-area .el-table {
        background: transparent !important;
        margin: 0 -8px !important;
    }

    /* 移除表格边框 */
    .project-main-area .el-table::before,
    .project-main-area .el-table::after {
        display: none !important;
    }

    /* 优化分页区域 */
    .project-main-area .el-pagination {
        padding: 16px 8px !important;
        background-color: #fff !important;
        border-radius: 12px !important;
        margin: 8px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    }
}

/* 桌面端样式 */
@media (min-width: 1024px) {
    .is-mobile {
        display: none !important;
    }
} 

/* 移动版表格样式覆盖 */
.is-mobile .el-table {
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
}

.is-mobile .el-table__body-wrapper {
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
}

.is-mobile .el-table__inner-wrapper {
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
}

/* 确保移动版表格内容完全展开 */
.mobile-task-table-container {
    overflow: visible !important;
}

/* 修复移动版表格内部滚动问题 */
.mobile-task-cell {
    overflow: visible !important;
}

/* 移动版任务标签横向排布 */
.mobile-task-meta {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    margin-bottom: 5px !important;
    align-items: center !important;
}

.mobile-task-tag {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
} 