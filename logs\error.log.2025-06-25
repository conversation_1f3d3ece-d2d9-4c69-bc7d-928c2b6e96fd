2025-06-25 16:48:47 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:48:47 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:48:49 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:48:49 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:48:50 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:48:50 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:48:51 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:48:51 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:01 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:01 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:02 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:02 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:08 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:08 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:09 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:09 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:09 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:09 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:15 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:15 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:23 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:23 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:25 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:25 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:39 - ERROR - Exception on /static/images/default_avatar.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-25 16:49:39 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:39 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-25 16:49:39 - ERROR - CDN静态文件服务错误: js/zh-cn.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-25 16:49:56 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:56 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 16:49:57 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 16:49:57 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 17:27:42 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 17:27:42 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 17:27:43 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    {{ row.name }}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 17:27:43 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 17:44:32 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    }">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 17:44:32 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 17:44:33 - ERROR - Exception on /projectmanagement/main [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\project_management.py", line 1220, in project_main
    return render_template('project_management_main.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 1, in top-level template code
    {% extends "base_layout.html" %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\base_layout.html", line 191, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_main.html", line 919, in block 'content'
    {% include 'project_management_mobile_received.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\templates\project_management_mobile_received.html", line 112, in top-level template code
    }">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 485, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'row' is undefined
2025-06-25 17:44:33 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-25 23:49:52 - ERROR - CDN静态文件服务错误: js/axios.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
