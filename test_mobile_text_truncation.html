<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端文本截断测试</title>
    <link rel="stylesheet" href="static/css/project_management_mobile.css">
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        .test-title {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-label {
            font-weight: bold;
            margin-bottom: 10px;
            color: #666;
        }
        
        /* 模拟移动端表格容器 - 居中显示 */
        .test-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            padding: 0 8px;
            box-sizing: border-box;
        }

        /* 模拟移动端卡片布局 - 居中显示 */
        .mock-mobile-card {
            display: block;
            margin: 12px auto; /* 居中显示 */
            max-width: calc(100vw - 32px); /* 与实际卡片宽度一致 */
            width: calc(100vw - 32px); /* 明确设置宽度 */
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            position: relative;
            border: 1px solid #f0f0f0;
            overflow: hidden;
            padding: 12px;
        }
        
        .mock-task-content {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .mock-checkbox {
            width: 16px;
            height: 16px;
            border: 2px solid #ddd;
            border-radius: 3px;
            flex-shrink: 0;
        }
        
        .mock-task-name {
            flex: 1;
            min-width: 0;
        }
        
        /* 应用实际的CSS规则 - 更严格的宽度控制 */
        .task-name-text,
        .overdue-name,
        .completed-name {
            display: inline-block !important;
            white-space: nowrap !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            max-width: calc(100vw - 160px) !important;
            word-break: break-all !important;
            vertical-align: middle !important;
        }

        .mobile-text-ellipsis {
            white-space: nowrap !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            max-width: calc(100vw - 150px) !important;
            display: inline-block !important;
            vertical-align: top !important;
        }
        
        .info-text {
            font-size: 12px;
            color: #999;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="test-title">移动端文本截断效果测试</h2>
        
        <div class="test-card">
            <div class="test-label">测试1: 短任务名称（正常显示）</div>
            <div class="mock-mobile-card">
                <div class="mock-task-content">
                    <div class="mock-checkbox"></div>
                    <div class="mock-task-name">
                        <span class="task-name-text">完成项目报告</span>
                    </div>
                </div>
                <div class="info-text">✓ 短文本应该正常显示，不会被截断</div>
            </div>
        </div>
        
        <div class="test-card">
            <div class="test-label">测试2: 中等长度任务名称（应显示省略号）</div>
            <div class="mock-mobile-card">
                <div class="mock-task-content">
                    <div class="mock-checkbox"></div>
                    <div class="mock-task-name">
                        <span class="task-name-text">完成2024年第四季度项目管理系统的移动端适配和优化工作</span>
                    </div>
                </div>
                <div class="info-text">✓ 中等长度文本应该显示省略号，不会超出卡片边界</div>
            </div>
        </div>

        <div class="test-card">
            <div class="test-label">测试3: 12字符阈值测试（刚好触发省略号）</div>
            <div class="mock-mobile-card">
                <div class="mock-task-content">
                    <div class="mock-checkbox"></div>
                    <div class="mock-task-name">
                        <span class="task-name-text">这是一个十二个字符的任务名称</span>
                    </div>
                </div>
                <div class="info-text">✓ 12个字符以上会显示title属性和省略号</div>
            </div>
        </div>

        <div class="test-card">
            <div class="test-label">测试4: 超长任务名称（严格截断）</div>
            <div class="mock-mobile-card">
                <div class="mock-task-content">
                    <div class="mock-checkbox"></div>
                    <div class="mock-task-name">
                        <span class="task-name-text">完成2024年第四季度项目管理系统的移动端界面适配、响应式布局优化、用户体验改进、性能优化、兼容性测试以及相关文档编写工作</span>
                    </div>
                </div>
                <div class="info-text">✓ 超长文本应该被严格截断，确保不会导致水平滚动</div>
            </div>
        </div>
        
        <div class="test-card">
            <div class="test-label">测试4: 逾期任务名称</div>
            <div class="mock-mobile-card">
                <div class="mock-task-content">
                    <div class="mock-checkbox"></div>
                    <div class="mock-task-name">
                        <span class="overdue-name" style="color: #f56c6c;">完成移动端项目管理系统的界面优化和用户体验改进工作任务</span>
                    </div>
                </div>
                <div class="info-text">✓ 逾期任务应该保持红色样式并正确截断</div>
            </div>
        </div>
        
        <div class="test-card">
            <div class="test-label">测试5: 已完成任务名称</div>
            <div class="mock-mobile-card">
                <div class="mock-task-content">
                    <div class="mock-checkbox"></div>
                    <div class="mock-task-name">
                        <span class="completed-name" style="text-decoration: line-through; color: #909399;">完成移动端项目管理系统的响应式布局设计和实现工作</span>
                    </div>
                </div>
                <div class="info-text">✓ 已完成任务应该保持删除线样式并正确截断</div>
            </div>
        </div>
        
        <div class="test-card">
            <div class="test-label">测试6: 使用mobile-text-ellipsis类</div>
            <div class="mock-mobile-card">
                <div class="mock-task-content">
                    <div class="mock-checkbox"></div>
                    <div class="mock-task-name">
                        <span class="mobile-text-ellipsis">这是一个使用mobile-text-ellipsis通用样式类的超长任务名称测试文本内容</span>
                    </div>
                </div>
                <div class="info-text">✓ 通用省略样式类应该正确工作</div>
            </div>
        </div>
        
        <div class="test-card">
            <div class="test-label">当前设置说明</div>
            <div style="padding: 10px; background: #f0f9ff; border-radius: 8px; font-size: 14px;">
                <p><strong>进一步优化的最大宽度设置：</strong></p>
                <ul>
                    <li>任务名称: calc(100vw - 160px) ⬇️ (让省略号更早出现)</li>
                    <li>通用省略类: calc(100vw - 150px) ⬇️</li>
                    <li>移动端卡片内容: calc(100vw - 140px) ⬇️</li>
                    <li>普通表格单元格: calc(100vw - 120px) ⬇️</li>
                </ul>
                <p><strong>字符数阈值调整：</strong></p>
                <ul>
                    <li>任务名称title显示: 12个字符 ⬇️ (原15个字符)</li>
                    <li>抽屉标题截断: 16个字符 ⬇️ (原20个字符)</li>
                    <li>更早触发省略号显示</li>
                </ul>
                <p><strong>预留空间分配：</strong></p>
                <ul>
                    <li>卡片边距: 16px (左右各8px)</li>
                    <li>卡片内边距: 24px (左右各12px)</li>
                    <li>复选框宽度: 24px</li>
                    <li>元素间距: 8px</li>
                    <li>其他元素: 20px</li>
                    <li>安全边距: 68px ⬆️</li>
                    <li><strong>总计: 160px (增加20px)</strong></li>
                </ul>
                <p><strong>卡片居中显示：</strong></p>
                <ul>
                    <li>使用 margin: 12px auto 实现水平居中</li>
                    <li>限制最大宽度为 calc(100vw - 16px)</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟移动端适配脚本的行为
        document.addEventListener('DOMContentLoaded', function() {
            const taskNameElements = document.querySelectorAll('.task-name-text, .overdue-name, .completed-name');
            
            taskNameElements.forEach(function(element) {
                const text = element.textContent.trim();
                if (text.length > 12) {
                    element.setAttribute('title', text);
                }
                
                // 应用更严格的宽度限制，让省略号更早出现
                element.style.maxWidth = 'calc(100vw - 160px)';
                element.style.whiteSpace = 'nowrap';
                element.style.overflow = 'hidden';
                element.style.textOverflow = 'ellipsis';
                element.style.display = 'inline-block';
                element.style.verticalAlign = 'middle';
            });
            
            console.log('移动端文本截断测试页面已加载');
        });
    </script>
</body>
</html>
