2025-06-23 17:00:44 - ERROR - CDN静态文件服务错误: js/zh-cn.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-23 17:13:15 - ERROR - CDN静态文件服务错误: js/axios.min.js.map, 错误: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-23 22:40:34 - ERROR - Exception on /static/uploads/announcements/20250116_155049_image.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-23 22:40:34 - ERROR - Exception on /static/uploads/announcements/20250116_162001_1737015315237_23E0A6BB-E3C6-4767-B3E5-62ADE1B53AAB.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-23 22:40:34 - ERROR - Exception on /static/uploads/announcements/20250116_162049_1737015389911_BD9B00FA-36A3-462a-91BB-BC3205663C83.png [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-23 22:40:34 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-23 22:40:34 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-23 22:40:34 - ERROR - 500错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-23 22:40:34 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-23 22:40:34 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-23 22:40:34 - ERROR - Request finalizing failed with an error while handling an error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 920, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 941, in finalize_request
    response = self.process_response(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1319, in process_response
    response = self.ensure_sync(func)(response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\zzz\wxzt\app.py", line 178, in add_security_headers
    response.set_etag(response.get_etag()[0])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\sansio\response.py", line 507, in set_etag
    self.headers["ETag"] = quote_etag(etag, weak)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\http.py", line 909, in quote_etag
    if '"' in etag:
       ^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
