/**
 * 项目管理系统 - 任务处理模块
 * 处理添加任务和任务相关功能
 */

/**
 * 初始化任务处理功能
 * @param {Object} currentProject 当前项目引用
 * @param {Object} tasks 任务列表引用
 * @param {Object} Vue Vue实例
 * @param {Object} ElementPlus ElementPlus实例
 * @param {Object} drawer 抽屉引用
 * @returns {Object} 包含任务处理功能的对象
 */
export function initializeTaskHandler(currentProject, tasks, currentPage,   totalTasks, Vue, ElementPlus, drawer) {
    const { ref, reactive, computed, watch } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    // totalTasks.value = 0;
    // currentPage.value = 1;
    // 初始化任务富文本编辑器
    const initTaskEditor = (initialContent = '') => {
        // 使用延迟初始化，确保DOM已完全渲染
        setTimeout(() => {
            // 确保之前的编辑器实例被销毁
            if (window.taskEditor) {
                window.taskEditor.destroy();
                window.taskEditor = null;
            }
            if (window.taskToolbar) {
                window.taskToolbar.destroy();
                window.taskToolbar = null;
            }
            
            // 确保DOM元素已经渲染
            const richEditorContent = document.getElementById('taskEditorContent');
            const richEditorToolbar = document.getElementById('taskEditorToolbar');
            
            if (!richEditorContent || !richEditorToolbar) {
                console.error('找不到任务编辑器DOM元素', {
                    content: !!richEditorContent,
                    toolbar: !!richEditorToolbar
                });
                return;
            }
            
            try {
                if (typeof window.wangEditor === 'undefined') {
                    console.error('编辑器组件未加载');
                    return;
                }
                
                const { createEditor, createToolbar } = window.wangEditor;
                
                // 配置编辑器
                const editorConfig = {
                    placeholder: '请输入任务描述...',
                    onChange: (editor) => {
                        // 更新编辑器内容到Vue的数据模型
                        taskDialog.value.form.description = editor.getHtml();
                    },
                    MENU_CONF: {
                        uploadImage: {
                            server: '/projectmanagement/proxy/upload_file',
                            fieldName: 'files',
                            headers: {},
                            maxFileSize: 10 * 1024 * 1024, // 设置最大文件大小为10MB
                            customInsert(res, insertFn) {
                                // 处理上传成功的响应
                                if (res.success && res.files && res.files.length > 0) {
                                    const file = res.files[0];
                                    insertFn(file.url, file.original_name, file.url);
                                } else {
                                    ElementPlus.ElMessage.error('图片上传失败');
                                }
                            },
                            onError(file, err, res) {
                                console.error('图片上传错误', file, err, res);
                                ElementPlus.ElMessage.error('图片上传错误: ' + (res?.message || err?.message || '未知错误'));
                            }
                        },
                        // 添加视频上传配置
                        uploadVideo: {
                            server: '/projectmanagement/proxy/upload_file',
                            fieldName: 'files',
                            headers: {},
                            maxFileSize: 50 * 1024 * 1024, // 设置最大文件大小为50MB
                            allowedFileTypes: ['video/*'], // 允许的文件类型
                            customInsert(res, insertFn) {
                                // 处理上传成功的响应
                                if (res.success && res.files && res.files.length > 0) {
                                    const file = res.files[0];
                                    insertFn(file.url);
                                } else {
                                    ElementPlus.ElMessage.error('视频上传失败');
                                }
                            },
                            onError(file, err, res) {
                                console.error('视频上传错误', file, err, res);
                                ElementPlus.ElMessage.error('视频上传错误: ' + (res?.message || err?.message || '未知错误'));
                            }
                        }
                    }
                };
                
                // 创建编辑器
                window.taskEditor = createEditor({
                    selector: '#taskEditorContent',
                    html: initialContent,
                    config: editorConfig,
                    mode: 'default',
                });
                
                // 创建工具栏
                window.taskToolbar = createToolbar({
                    editor: window.taskEditor,
                    selector: '#taskEditorToolbar',
                    mode: 'default',
                    config: {
                        toolbarKeys: [
                            'bold',
                            'italic',
                            'underline',
                            'uploadImage',
                            'uploadVideo',  // 添加视频上传按钮
                            'justifyLeft',
                            'justifyCenter',
                            'justifyRight',
                            'undo',
                            'redo'
                        ]
                    }
                });
                
                // 为编辑器添加自动焦点（移动端除外，避免键盘弹出遮挡内容）
                setTimeout(() => {
                    if (window.taskEditor && window.innerWidth > 1023) {
                        window.taskEditor.focus();
                    }
                }, 100);
            } catch (error) {
                console.error('初始化任务富文本编辑器失败:', error);
                ElementPlus.ElMessage.error('初始化编辑器失败，请刷新页面重试');
            }
        }, 50); // 添加50毫秒延迟，确保DOM已渲染
    };
    
    // 任务对话框状态
    const taskDialog = ref({
        visible: false,
        title: '添加任务',
        isEditMode: false,
        isHistoricalMode: false,
        isSubTaskMode: false,
        parentTaskId: null,
        taskId: null,
        loading: false,
        showAdvanced: false,
        form: {
            name: '',
            description: '',
            deadline: '',
            urgency: '一般',
            assignees: [],
            isImmediate: true,
            ccUsers: [] // 添加抄送人字段
        },
        rules: {
            name: [
                { required: true, message: '请输入任务名称', trigger: 'blur' },
                { min: 2, max: 50, message: '任务名称长度应在2至50个字符之间', trigger: 'blur' }
            ],
            description: [
                { max: 5000, message: '任务描述不能超过5000个字符', trigger: 'blur' }
            ],
            deadline: [
                { required: true, message: '请选择截止日期', trigger: 'change' }
            ],
            assignees: [
                { type: 'array', required: true, message: '请至少选择一个负责人', trigger: 'change' }
            ]
        },
        urgencyOptions: [
            { label: '紧急', value: '紧急' },
            { label: '一般', value: '一般' }
        ],
        availableAssignees: []
    });
    
    // 打开任务对话框
    const showAddTaskDialog = () => {
        // 重置表单
        taskDialog.value.form = {
            name: '',
            description: '',
            deadline: '',
            urgency: '一般',
            assignees: [],
            isImmediate: true,
            ccUsers: []
        };
        
        // 重置编辑模式和历史模式
        taskDialog.value.isEditMode = false;
        taskDialog.value.isHistoricalMode = false;
        taskDialog.value.taskId = null;
        taskDialog.value.showAdvanced = false;  // 重置高级选项显示状态
        
        // 检查当前是否有选中的项目
        if (!currentProject.value || !currentProject.value.id) {
            ElMessage({
                message: '请先选择一个项目',
                type: 'warning'
            });
            return;
        }
        
        // 设置对话框标题
        taskDialog.value.title = `添加任务: ${currentProject.value.name}`;
        
        // 显示对话框
        taskDialog.value.visible = true;
        
        // 加载项目成员作为可分配人员
        loadProjectMembers();
        
        // 在DOM更新后初始化富文本编辑器，增加延迟时间确保对话框完全打开
        setTimeout(() => {
            initTaskEditor('');
        }, 300);
    };
    
    // 新增：打开登记未完成的任务对话框
    const showAddHistoricalTaskDialog = () => {
        // 重置表单
        taskDialog.value.form = {
            name: '',
            description: '',
            deadline: '',
            urgency: '一般',
            assignees: [],
            isImmediate: true,
            ccUsers: []
        };
        
        // 设置为历史任务模式，但不是编辑模式
        taskDialog.value.isEditMode = false;
        taskDialog.value.isHistoricalMode = true;
        taskDialog.value.taskId = null;
        taskDialog.value.showAdvanced = false;  // 重置高级选项显示状态
        
        // 检查当前是否有选中的项目
        if (!currentProject.value || !currentProject.value.id) {
            ElMessage({
                message: '请先选择一个项目',
                type: 'warning'
            });
            return;
        }
        
        // 设置对话框标题
        taskDialog.value.title = `登记未完成任务: ${currentProject.value.name}`;
        
        // 显示对话框
        taskDialog.value.visible = true;
        
        // 加载项目成员作为可分配人员
        loadProjectMembers();
        
        // 在DOM更新后初始化富文本编辑器，增加延迟时间确保对话框完全打开
        setTimeout(() => {
            initTaskEditor('');
        }, 300);
    };
    
    // 打开添加子任务对话框
    const showAddSubTaskDialog = (parentTaskId) => {
        console.log("showAddSubTaskDialog called with parentTaskId:", parentTaskId);
        console.log("drawer state:", {
            taskId: drawer.value.taskId,
            task: drawer.value.task,
            isChildTask: drawer.value.task && drawer.value.task.is_child
        });
        
        // 重置表单
        taskDialog.value.form = {
            name: '',
            description: '',
            deadline: '',
            urgency: '一般',
            assignees: [],
            parent_id: parentTaskId, // 设置父任务ID
            isImmediate: true,
            ccUsers: []
        };
        
        // 重置编辑模式和历史模式
        taskDialog.value.isEditMode = false;
        taskDialog.value.isHistoricalMode = false;
        taskDialog.value.taskId = null;
        taskDialog.value.showAdvanced = false;  // 重置高级选项显示状态
        
        // 检查是否有选中的任务
        if (!drawer || !drawer.value || !drawer.value.taskId) {
            ElMessage({
                message: '请先选择一个任务',
                type: 'warning'
            });
            return;
        }
        
        // 检查选中的任务是否为子任务
        if (drawer.value.task && drawer.value.task.is_child) {
            console.log("子任务不能添加子任务. Task details:", drawer.value.task);
            ElMessage({
                message: '子任务不能添加子任务',
                type: 'warning'
            });
            return;
        }
        
        // 检查当前是否有选中的项目
        if (!currentProject.value || !currentProject.value.id) {
            ElMessage({
                message: '请先选择一个项目',
                type: 'warning'
            });
            return;
        }
        
        // 设置对话框标题
        const parentTaskName = drawer.value.task ? drawer.value.task.name : '';
        taskDialog.value.title = `添加子任务: ${parentTaskName}`;
        taskDialog.value.form.parent_id = drawer.value.taskId;
        
        // 显示对话框
        taskDialog.value.visible = true;
        
        // 加载项目成员作为可分配人员
        loadProjectMembers();
        
        // 在DOM更新后初始化富文本编辑器，增加延迟时间确保对话框完全打开
        setTimeout(() => {
            initTaskEditor('');
        }, 300);
    };
    
    // 显示编辑任务对话框
    const showEditTaskDialog = () => {
        // 确保抽屉中有任务数据
        if (!drawer.value.task) {
            ElMessage.error('无法获取任务数据，请重试');
            return;
        }
        
        // 设置初始加载标志为true，防止watch触发自动更新抄送人
        isInitialLoading.value = true;
        
        // 设置对话框标题
        taskDialog.value.title = '编辑任务';
        
        // 设置为编辑模式
        taskDialog.value.isEditMode = true;
        taskDialog.value.isHistoricalMode = false;
        
        // 初始化表单（直接创建对象，不调用外部函数）
        taskDialog.value.form = {
            name: '',
            description: '',
            deadline: '',
            urgency: '一般',
            assignees: [],
            isImmediate: true,
            ccUsers: []
        };
        
        // 填充表单数据
        const task = drawer.value.task;
        taskDialog.value.form.name = task.name;
        taskDialog.value.form.description = task.description || '';
        taskDialog.value.form.deadline = task.deadline || '';
        taskDialog.value.form.urgency = task.urgency || '一般';
        taskDialog.value.form.isImmediate = task.is_immediate === 1;
        
        // 设置任务ID
        taskDialog.value.taskId = task.id;
        
        // 设置项目ID
        taskDialog.value.projectId = task.project_id;
        
        // 设置父任务ID（如果有）
        if (task.parent_id) {
            taskDialog.value.parentTaskId = task.parent_id;
        } else {
            taskDialog.value.parentTaskId = null;
        }
        
        // 加载项目成员作为可选负责人
        loadProjectMembers();
        
        // 设置负责人（需要等待可选负责人加载完成）
        const assigneeNames = task.assignees ? task.assignees.split(',') : [];
        
        // 使用setTimeout确保在可选负责人加载完成后设置
        if (assigneeNames.length > 0 && taskDialog.value.availableAssignees.length > 0) {
            const assigneeIds = taskDialog.value.availableAssignees
                .filter(assignee => assigneeNames.includes(assignee.label))
                .map(assignee => assignee.value);
                
            taskDialog.value.form.assignees = assigneeIds;
        } else {
            // 如果负责人还没加载好，设置一个定时器等待加载
            const checkInterval = setInterval(() => {
                if (taskDialog.value.availableAssignees.length > 0) {
                    clearInterval(checkInterval);
                    
                    const assigneeIds = taskDialog.value.availableAssignees
                        .filter(assignee => assigneeNames.includes(assignee.label))
                        .map(assignee => assignee.value);
                        
                    taskDialog.value.form.assignees = assigneeIds;
                }
            }, 100);
            
            // 设置超时，避免无限等待
            setTimeout(() => {
                clearInterval(checkInterval);
            }, 5000);
        }
        
        // 加载任务抄送人
        fetch(`/projectmanagement/api/task_cc_users/${task.id}`)
            .then(response => response.json())
            .then(ccData => {
                if (ccData.success) {
                    // 直接使用后端返回的抄送人数据
                    taskDialog.value.form.ccUsers = ccData.cc_users;
                }
                
                // 所有数据加载完成后，重置初始加载标志
                // 延迟一点执行，确保所有数据都已经设置完成
                setTimeout(() => {
                    isInitialLoading.value = false;
                    console.log('初始加载完成，后续负责人变化将自动更新抄送人');
                }, 500);
            })
            .catch(error => {
                console.error('获取任务抄送人失败:', error);
                // 即使出错也需要重置标志
                isInitialLoading.value = false;
            });
        
        // 显示对话框
        taskDialog.value.visible = true;
        
        // 在DOM更新后初始化富文本编辑器，并设置初始内容，增加延迟确保对话框完全打开
        setTimeout(() => {
            initTaskEditor(task.description || '');
        }, 300);
    };
    
    // 获取负责人的领导
    const getAssigneeLeaders = async (assigneeIds) => {
        if (!assigneeIds || assigneeIds.length === 0) {
            return [];
        }
        
        try {
            // 构建请求URL，包含所有负责人ID
            const queryParams = assigneeIds.map(id => `assignee_id=${id}`).join('&');
            const response = await fetch(`/api/assignee_leaders?${queryParams}`);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '获取负责人领导失败');
            }
            
            const data = await response.json();
            
            if (data.success && data.leaders) {
                return data.leaders;
            } else {
                return [];
            }
        } catch (error) {
            console.error('获取负责人领导失败:', error);
            ElMessage({
                message: '获取负责人领导失败，请稍后重试',
                type: 'warning'
            });
            return [];
        }
    };
    
    // 根据负责人获取其领导并更新抄送人列表
    const updateCcUsersWithLeaders = async (assigneeIds) => {
        if (!assigneeIds || assigneeIds.length === 0) {
            // 如果没有负责人，清空抄送人列表
            taskDialog.value.form.ccUsers = [];
            return;
        }
        
        try {
            // 获取所有负责人的领导
            const leaders = await getAssigneeLeaders(assigneeIds);
            
            // 清空现有抄送人列表
            taskDialog.value.form.ccUsers = [];
            
            // 将领导添加到抄送人列表中
            if (leaders && leaders.length > 0) {
                // 先检查现有抄送人中是否已经包含了领导
                taskDialog.value.form.ccUsers.forEach(user => {
                    const leaderIndex = leaders.findIndex(leader => leader.userid === user.userid);
                    if (leaderIndex !== -1) {
                        // 如果已经包含，从leaders中移除
                        leaders.splice(leaderIndex, 1);
                    }
                });
                
                // 添加剩余的领导到抄送人列表
                leaders.forEach(leader => {
                    taskDialog.value.form.ccUsers.push({
                        userid: leader.userid,
                        name: leader.name
                    });
                });
            }
        } catch (error) {
            console.error('获取负责人领导失败:', error);
        }
    };
    
    // 添加一个标志，用于标记是否是初始加载
    const isInitialLoading = Vue.ref(false);
    
    // 监听负责人变化，更新抄送人列表
    watch(() => taskDialog.value.form.assignees, (newAssignees, oldAssignees) => {
        // 如果是初始加载状态，不自动更新抄送人
        if (isInitialLoading.value) {
            console.log('初始加载状态，不自动更新抄送人');
            return;
        }

        // 如果负责人没有变化，不更新抄送人
        if (JSON.stringify(newAssignees) === JSON.stringify(oldAssignees)) {
            return;
        }

        // 清空并重新获取抄送人列表
        updateCcUsersWithLeaders(newAssignees);
    });
    
    // 加载项目成员
    const loadProjectMembers = () => {
        if (!currentProject.value || !currentProject.value.id) {
            return Promise.reject(new Error('无效的项目ID'));
        }
        
        taskDialog.value.loading = true;
        
        // 修改：先获取当前项目成员，然后获取全部在职员工
        return fetch(`/projectmanagement/api/project_members?project_id=${currentProject.value.id}`)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '获取项目成员失败');
                    });
                }
                return response.json();
            })
            .then(projectData => {
                if (!projectData.success) {
                    throw new Error(projectData.message || '获取项目成员失败');
                }
                
                // 获取项目成员ID列表
                const projectMemberIds = (projectData.members || []).map(member => member.id);
                
                // 然后获取所有在职员工
                return fetch('/projectmanagement/api/employees')
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取员工数据失败');
                            });
                        }
                        return response.json();
                    })
                    .then(employeeData => {
                        if (!employeeData.success) {
                            throw new Error(employeeData.message || '获取员工数据失败');
                        }
                        
                        // 转换成可分配的成员列表，标记是否为项目成员
                        taskDialog.value.availableAssignees = (employeeData.employees || []).map(employee => ({
                            value: employee.id,
                            label: employee.name,
                            department: employee.department,
                            isProjectMember: projectMemberIds.includes(Number(employee.id)),
                        }));
                        
                        return employeeData.employees;
                    });
            })
            .catch(error => {
                ElMessage({
                    message: error.message,
                    type: 'error'
                });
                return [];
            })
            .finally(() => {
                taskDialog.value.loading = false;
            });
            
            // 如果是编辑模式，加载任务的抄送人
            if (taskDialog.value.isEditMode && taskDialog.value.taskId) {
                fetch(`/projectmanagement/api/task_cc_users/${taskDialog.value.taskId}`)
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取任务抄送人失败');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.cc_users) {
                            taskDialog.value.form.ccUsers = data.cc_users;
                        }
                    })
                    .catch(error => {
                        console.error('获取任务抄送人错误:', error);
                        ElMessage({
                            message: '获取任务抄送人失败，请稍后重试',
                            type: 'warning'
                        });
                    });
            }
    };
    
    // 提交添加任务
    const submitAddTask = (formRef) => {
        if (!formRef) return;
        
        // 从富文本编辑器获取最新内容
        if (window.taskEditor) {
            taskDialog.value.form.description = window.taskEditor.getHtml();
        }
        
        formRef.validate((valid) => {
            if (!valid) {
                return false;
            }
            
            // 检查是否选择了负责人
            if (!taskDialog.value.form.assignees || taskDialog.value.form.assignees.length === 0) {
                ElMessage({
                    message: '请至少选择一个负责人!',
                    type: 'warning'
                });
                return false;
            }
            
            // 显示加载状态
            taskDialog.value.loading = true;
            
            // 获取当前选择的负责人中，哪些不是项目成员
            const selectedAssigneeIds = taskDialog.value.form.assignees || [];
            const nonProjectMembers = selectedAssigneeIds.filter(id => {
                const assignee = taskDialog.value.availableAssignees.find(a => a.value === id);
                return assignee && !assignee.isProjectMember;
            });
            
            // 构建提交数据
            const taskData = {
                name: taskDialog.value.form.name,
                description: taskDialog.value.form.description,
                deadline: taskDialog.value.form.deadline || null,
                urgency: taskDialog.value.form.urgency,
                assignee_ids: selectedAssigneeIds,
                non_project_member_ids: nonProjectMembers, // 添加非项目成员ID列表
                is_historical: taskDialog.value.isHistoricalMode, // 新增：指示这是历史任务
                is_immediate: taskDialog.value.form.isImmediate
            };
            
            // 添加抄送人字段
            if (taskDialog.value.form.ccUsers && taskDialog.value.form.ccUsers.length > 0) {
                taskData.cc_users = taskDialog.value.form.ccUsers;
            }
            
            // 如果是创建新任务，需要添加项目ID
            if (!taskDialog.value.isEditMode) {
                taskData.project_id = currentProject.value.id;
            
                // 如果是子任务，添加父任务ID
                if (taskDialog.value.form.parent_id) {
                    taskData.parent_id = taskDialog.value.form.parent_id;
                }
            }
            
            // 根据是否是编辑模式，决定使用哪个接口
            const url = taskDialog.value.isEditMode 
                ? `/projectmanagement/api/update_task/${taskDialog.value.taskId}`
                : '/projectmanagement/api/create_task';
            
            console.log('提交的任务数据:', taskData, '到URL:', url);
            
            // 发送请求
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(taskData)
            }).then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '操作失败');
                    });
                }
                return response.json();
            }).then(data => {
                if (data.success) {
                    // 如果是编辑模式，更新任务列表中的对应任务
                    if (taskDialog.value.isEditMode) {
                        const index = tasks.value.findIndex(t => t.id === taskDialog.value.taskId);
                        if (index !== -1 && data.task) {
                            tasks.value[index] = data.task;
                        }
                        
                        // 如果抽屉中显示的是当前任务，也更新抽屉中的任务
                        if (drawer.value.task && drawer.value.task.id === taskDialog.value.taskId) {
                            drawer.value.task = data.task;
                            drawer.value.title = `任务详情: ${data.task.name}`;
                        }
                    } else {
                        // 创建新任务，判断是否是子任务
                        // if (data.task && taskDialog.value.form.parent_id) {
                            // 如果是子任务，重新加载整个任务列表以确保正确排序
                            const loadingInstance = ElementPlus.ElLoading.service({
                                target: 'body',
                                text: '重新加载任务列表...'
                            });
                            
                            fetch(`/projectmanagement/api/project_tasks?project_id=${currentProject.value.id}`)
                                .then(response => response.json())
                                .then(result => {
                                    if (result.success) {
                                        tasks.value = result.tasks || [];
                                         if (result.pagination && typeof result.pagination.total !== 'undefined') {
                                            currentPage.value = 1;
                                            totalTasks.value = result.pagination.total;
                                        } else {
                                            // 兼容旧接口，如果没有返回分页信息，使用任务数组长度
                                            totalTasks.value = tasks.value.length;
                                        }
                                    }
                                    loadingInstance.close();
                                })
                                .catch(() => {
                                    // 如果重新加载失败，至少将新创建的任务添加到列表中
                                    tasks.value.unshift(data.task);
                                    loadingInstance.close();
                                });
                        // } else {
                        //     // 普通任务，添加到列表顶部
                        //     tasks.value.unshift(data.task);
                        // }
                    }
                    
                    // 关闭对话框
                    taskDialog.value.visible = false;
                    
                    // 显示成功消息
                    let successMessage;
                    if (taskDialog.value.isEditMode) {
                        successMessage = '任务更新成功';
                    } else if (taskDialog.value.isHistoricalMode) {
                        successMessage = '历史任务登记成功';
                    } else {
                        successMessage = '任务创建成功';
                    }
                    
                    ElMessage({
                        message: successMessage,
                        type: 'success'
                    });
                } else {
                    throw new Error(data.message || '操作失败');
                }
            }).catch(error => {
                ElMessage({
                    message: error.message,
                    type: 'error'
                });
            }).finally(() => {
                taskDialog.value.loading = false;
            });
        });
    };
    
    // 关闭任务对话框
    const closeTaskDialog = () => {
        // 销毁富文本编辑器实例
        if (window.taskEditor) {
            window.taskEditor.destroy();
            window.taskEditor = null;
        }
        if (window.taskToolbar) {
            window.taskToolbar.destroy();
            window.taskToolbar = null;
        }
        
        taskDialog.value.visible = false;
        // 触发一个自定义事件，通知父组件重置全选状态
        document.dispatchEvent(new CustomEvent('reset-select-all-assignees'));
        
        // 重置初始加载标志
        isInitialLoading.value = false;
    };
    
    // 格式化日期
    const formatDate = (date) => {
        if (!date) return '';
        
        const d = new Date(date);
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    };
    
    // 禁用日期的选择器
    // 修改为允许历史模式下选择过去日期
    const disablePastDates = (time) => {
        // 如果是历史任务模式，不禁用任何日期
        if (taskDialog.value.isHistoricalMode) {
            return false;
        }
        
        // 获取今天的日期（不包含时间）
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        return time.getTime() < today.getTime();
    };
    
    return {
        taskDialog,
        showAddTaskDialog,
        showAddHistoricalTaskDialog,  // 新增：暴露登记未完成任务方法
        showAddSubTaskDialog,
        showEditTaskDialog,
        submitAddTask,
        closeTaskDialog,
        formatDate,
        disablePastDates,
        getAssigneeLeaders,
        updateCcUsersWithLeaders,
        initTaskEditor
    };
} 