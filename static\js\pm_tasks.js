// 项目任务管理模块

/**
 * 初始化任务管理功能
 * @param {Object} currentView 当前视图引用
 * @param {Object} currentProject 当前项目引用
 * @param {Object} searchQuery 搜索查询引用
 * @param {Object} tasks 任务列表引用
 * @param {Object} tableLoading 表格加载状态引用
 * @param {Object} drawer 抽屉引用
 * @param {Object} projects 项目列表引用
 * @param {Object} ElementPlus ElementPlus实例
 * @returns {Object} 包含任务管理功能的对象
 */
export function initializeTaskManagement(
    currentView,
    currentProject,
    searchQuery,
    tasks,
    totalTasks,
    currentPage,
    tableLoading,
    drawer,
    projects,
    ElementPlus
) {
    // 搜索任务
    const searchTasks = () => {
        // if (!searchQuery.value.trim()) return;
        
        // 设置当前视图为搜索视图
        currentView.value = 'search';
        
        // 更新URL，不刷新页面
        const url = new URL(window.location.href);
        url.searchParams.set('view_type', 'search');
        url.searchParams.set('query', searchQuery.value);
        window.history.pushState({}, document.title, url);
        
        // 显示加载状态
        tableLoading.value = true;
        tasks.value = [];
        
        // 使用AJAX获取搜索结果
        fetch(`/projectmanagement/api/search?query=${encodeURIComponent(searchQuery.value.trim())}`)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '搜索失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                tableLoading.value = false;
                if (data.success) {
                    tasks.value = data.tasks || [];
                    if (data.pagination && typeof data.pagination.total !== 'undefined') {
                        currentPage.value = 1;
                        totalTasks.value = data.pagination.total;
                    } else {
                        // 兼容旧接口，如果没有返回分页信息，使用任务数组长度
                        totalTasks.value = tasks.value.length;
                    }
                    // 重置分页到第一页
                    if (typeof currentPage !== 'undefined') {
                        currentPage.value = 1;
                    }
                    
                    // 清除任务抽屉
                    if (drawer.value.visible) {
                        closeDrawer();
                    }
                } else {
                    throw new Error(data.message || '搜索失败');
                }
            })
            .catch(error => {
                tableLoading.value = false;
                ElementPlus.ElMessage({
                    message: error.message,
                    type: 'error'
                });
            });
    };

    // 返回行的类名
    const getRowClass = (row) => {
        let classes = [];
        if (row.is_child) classes.push('task-child');
        if (row.completed_date) classes.push('completed-task');
        if (row.id === drawer.value.taskId) classes.push('is-selected');
        return classes.join(' ');
    };

    // 获取完成任务按钮的提示文本
    const getCompleteTitle = (task) => {
        if (task.completed_date) {
            return `已完成 (${task.completed_date})`;
        } else if (task.can_complete) {
            return "点击标记为已完成";
        } else {
            return "只有发起人或负责人可以完成任务";
        }
    };

    // 初始化回复富文本编辑器
    const initReplyEditor = () => {
        // 确保之前的编辑器实例被销毁
        if (window.replyEditor) {
            window.replyEditor.destroy();
            window.replyEditor = null;
        }
        if (window.replyToolbar) {
            window.replyToolbar.destroy();
            window.replyToolbar = null;
        }
        
        // 确保DOM元素已经渲染
        const richEditorContent = document.getElementById('replyEditorContent');
        const richEditorToolbar = document.getElementById('replyEditorToolbar');
        
        if (!richEditorContent || !richEditorToolbar) {
            console.error('找不到编辑器DOM元素', {
                content: !!richEditorContent,
                toolbar: !!richEditorToolbar
            });
            return;
        }
        
        try {
            if (typeof window.wangEditor === 'undefined') {
                console.error('编辑器组件未加载');
                return;
            }
            
            const { createEditor, createToolbar } = window.wangEditor;
            
            // 配置编辑器
            const editorConfig = {
                placeholder: '请输入回复内容...',
                onChange: (editor) => {
                    // 更新编辑器内容到Vue的数据模型
                    drawer.value.replyHtml = editor.getHtml();
                    drawer.value.replyText = editor.getText();
                },
                MENU_CONF: {
                    uploadImage: {
                        server: '/projectmanagement/proxy/upload_file',
                        fieldName: 'files',
                        headers: {},
                        maxFileSize: 10 * 1024 * 1024, // 设置最大文件大小为10MB
                        customInsert(res, insertFn) {
                            // 处理上传成功的响应
                            if (res.success && res.files && res.files.length > 0) {
                                const file = res.files[0];
                                insertFn(file.url, file.original_name, file.url);
                            } else {
                                ElementPlus.ElMessage.error('图片上传失败');
                            }
                        },
                        onError(file, err, res) {
                            console.error('图片上传错误', file, err, res);
                            ElementPlus.ElMessage.error('图片上传错误: ' + (res?.message || err?.message || '未知错误'));
                        }
                    },
                    // 添加视频上传配置
                    uploadVideo: {
                        server: '/projectmanagement/proxy/upload_file',
                        fieldName: 'files',
                        headers: {},
                        maxFileSize: 500 * 1024 * 1024, // 设置最大文件大小为50MB
                        allowedFileTypes: ['video/*'], // 允许的文件类型
                        customInsert(res, insertFn) {
                            // 处理上传成功的响应
                            if (res.success && res.files && res.files.length > 0) {
                                const file = res.files[0];
                                insertFn(file.url);
                            } else {
                                ElementPlus.ElMessage.error('视频上传失败');
                            }
                        },
                        onError(file, err, res) {
                            console.error('视频上传错误', file, err, res);
                            ElementPlus.ElMessage.error('视频上传错误: ' + (res?.message || err?.message || '未知错误'));
                        }
                    }
                }
            };
            
            // 创建编辑器
            window.replyEditor = createEditor({
                selector: '#replyEditorContent',
                html: '',
                config: editorConfig,
                mode: 'default',
            });
            
            // 创建工具栏
            window.replyToolbar = createToolbar({
                editor: window.replyEditor,
                selector: '#replyEditorToolbar',
                mode: 'default',
                config: {
                    toolbarKeys: [
                        'bold',
                        'italic',
                        'underline',
                        'uploadImage',
                        'uploadVideo'  // 添加视频上传按钮
                    ]
                }
            });
            
            // 为编辑器添加自动焦点（移动端除外，避免键盘弹出遮挡内容）
            setTimeout(() => {
                if (window.replyEditor && window.innerWidth > 1023) {
                    window.replyEditor.focus();
                }
            }, 100);
        } catch (error) {
            console.error('初始化富文本编辑器失败:', error);
            ElementPlus.ElMessage.error('初始化编辑器失败，请刷新页面重试');
        }
    };

    // 修改handleRowClick函数，在打开抽屉时初始化编辑器
    const handleRowClick = (row) => {
        drawer.value.taskId = row.id;
        drawer.value.visible = true;
        drawer.value.loading = true;
        drawer.value.error = null;
        drawer.value.title = `任务详情: ${row.name}`;
        drawer.value.projectName = row.project_name;
        drawer.value.replyMessage = '';
        drawer.value.replyHtml = '';
        drawer.value.replyText = '';
        
        // 获取任务详情
        fetch(`/projectmanagement/api/task/${row.id}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('获取任务详情失败');
            }
            return response.json();
        })
        .then(data => {
            drawer.value.loading = false;
            drawer.value.task = data.task;
            
            // 在DOM更新后初始化编辑器
            setTimeout(() => {
                initReplyEditor();
            }, 100);
        })
        .catch(error => {
            drawer.value.loading = false;
            drawer.value.error = error.message;
        });
    };

    // 修改closeDrawer函数，确保在抽屉关闭时销毁编辑器实例
    const closeDrawer = () => {
        // 销毁编辑器实例
        if (window.replyEditor) {
            window.replyEditor.destroy();
            window.replyEditor = null;
        }
        if (window.replyToolbar) {
            window.replyToolbar.destroy();
            window.replyToolbar = null;
        }
        
        drawer.value.visible = false;
        drawer.value.taskId = null;
        drawer.value.task = null;
        drawer.value.replyMessage = '';
        drawer.value.replyHtml = '';
        drawer.value.replyText = '';
        
        // 移除body上的drawer-open类
        document.body.classList.remove('drawer-open');
        
        // 更新URL，移除task_id参数
        const url = new URL(window.location.href);
        url.searchParams.delete('task_id');
        window.history.replaceState({}, document.title, url);
    };

    // 修改sendReply函数，从富文本编辑器获取内容
    const sendReply = () => {
        // 从编辑器获取内容
        const replyHtml = window.replyEditor ? window.replyEditor.getHtml() : '';
        const replyText = window.replyEditor ? window.replyEditor.getText() : '';
        
        // 验证回复内容是否为空
        if (!replyText.trim() || !drawer.value.taskId) {
            ElementPlus.ElMessage({
                message: '请输入回复内容',
                type: 'warning'
            });
            return;
        }
        
        // 显示加载中
        const loadingInstance = ElementPlus.ElLoading.service({
            target: '.reply-section',
            text: '发送中...'
        });
        
        // 发送回复到后端
        fetch('/projectmanagement/api/send_reply', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                task_id: drawer.value.taskId,
                reply: replyHtml // 发送HTML内容
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || '发送回复失败');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 清空编辑器内容
                if (window.replyEditor) {
                    window.replyEditor.setHtml('');
                }
                drawer.value.replyHtml = '';
                drawer.value.replyText = '';
                
                // 添加回复到任务日志中
                if (drawer.value.task && drawer.value.task.logs) {
                    // 将回复添加到沟通记录列表的顶部
                    drawer.value.task.logs.unshift(data.reply);
                }
                
                // 显示操作成功消息
                ElementPlus.ElMessage({
                    message: '回复已发送',
                    type: 'success'
                });
            } else {
                throw new Error(data.message || '发送回复失败');
            }
        })
        .catch(error => {
            ElementPlus.ElMessage({
                message: error.message,
                type: 'error'
            });
        })
        .finally(() => {
            // 关闭加载中
            loadingInstance.close();
        });
    };

    // 钉钉回复方法
    const sendDingReply = () => {
        // 获取富文本内容
        const replyHtml = window.replyEditor ? window.replyEditor.getHtml() : '';
        const replyText = window.replyEditor ? window.replyEditor.getText() : '';
        if (!replyText.trim() || !drawer.value.taskId) {
            ElementPlus.ElMessage({
                message: '请输入回复内容',
                type: 'warning'
            });
            return;
        }
        // 显示加载中
        const loadingInstance = ElementPlus.ElLoading.service({
            target: '.reply-section',
            text: '钉钉消息发送中...'
        });
        // 发送到后端（占位接口）
        fetch('/projectmanagement/api/send_dingtalk_reply', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                task_id: drawer.value.taskId,
                reply: replyHtml
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || '钉钉消息发送失败');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 清空编辑器内容
                if (window.replyEditor) {
                    window.replyEditor.setHtml('');
                }
                drawer.value.replyHtml = '';
                drawer.value.replyText = '';
                // 回显到沟通记录列表
                if (drawer.value.task && drawer.value.task.logs) {
                    drawer.value.task.logs.unshift(data.reply);
                }
                ElementPlus.ElMessage({
                    message: '钉钉消息已发送',
                    type: 'success'
                });
            } else {
                throw new Error(data.message || '钉钉消息发送失败');
            }
        })
        .catch(error => {
            ElementPlus.ElMessage({
                message: error.message,
                type: 'error'
            });
        })
        .finally(() => {
            loadingInstance.close();
        });
    };

    // 完成任务
    const completeTask = (task) => {
        if (!task.can_complete) return;
        
        // 如果是取消完成任务，直接执行不需要评论
        if (task.completed_date) {
            executeCompleteTask(task, "");
            return;
        }
        
        // 创建富文本编辑器对话框的DOM内容
        const dialogContent = `
            <div class="rich-editor-dialog">
                <div class="rich-editor-toolbar" id="richEditorToolbar" style="width: 100%;"></div>
                <div class="rich-editor-content" id="richEditorContent" style="height: 250px; width: 100%; border: 1px solid #dcdfe6; border-radius: 0 0 4px 4px; padding: 8px;"></div>
                <div style="margin-top: 10px;">
                    <span style="color: #909399; font-size: 12px;">支持图片和视频上传，可以拖拽文件到编辑区域或点击工具栏上传按钮</span>
                </div>
            </div>
        `;
        
        // 创建自定义对话框
        ElementPlus.ElMessageBox({
            title: '完成任务',
            dangerouslyUseHTMLString: true,
            message: dialogContent,
            showCancelButton: true,
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            customClass: 'task-complete-rich-dialog',
            // 增加宽度
            width: '700px',
            beforeClose: (action, instance, done) => {
                if (action === 'confirm') {
                    // 获取编辑器内容
                    const htmlContent = window.taskEditor ? window.taskEditor.getHtml() : '';
                    const textContent = window.taskEditor ? window.taskEditor.getText() : '';
                    
                    // 验证内容不能为空
                    if (!textContent.trim()) {
                        ElementPlus.ElMessage({
                            type: 'warning',
                            message: '评论内容不能为空'
                        });
                        return;
                    }
                    
                    // 执行任务完成操作
                    executeCompleteTask(task, htmlContent.trim());
                }
                
                // 在关闭对话框前销毁编辑器
                if (window.taskEditor) {
                    window.taskEditor.destroy();
                    window.taskEditor = null;
                }
                if (window.taskToolbar) {
                    window.taskToolbar.destroy();
                    window.taskToolbar = null;
                }
                
                done();
            }
        }).then(() => {
            // 用户点击了确认，但实际处理已在beforeClose中完成
        }).catch(() => {
            // 用户取消操作，清理编辑器
            if (window.taskEditor) {
                window.taskEditor.destroy();
                window.taskEditor = null;
            }
            if (window.taskToolbar) {
                window.taskToolbar.destroy();
                window.taskToolbar = null;
            }
        });
        
        // 初始化富文本编辑器
        setTimeout(() => {
            try {
                if (typeof window.wangEditor === 'undefined') {
                    console.error('编辑器组件未加载');
                    return;
                }
                
                // 确保DOM元素已经渲染
                const richEditorContent = document.getElementById('richEditorContent');
                const richEditorToolbar = document.getElementById('richEditorToolbar');
                
                if (!richEditorContent || !richEditorToolbar) {
                    console.error('找不到编辑器DOM元素', {
                        content: !!richEditorContent,
                        toolbar: !!richEditorToolbar
                    });
                    return;
                }
                
                const { createEditor, createToolbar } = window.wangEditor;
                
                // 配置编辑器
                const editorConfig = {
                    placeholder: '请输入完成任务的描述...',
                    MENU_CONF: {
                        uploadImage: {
                            server: '/projectmanagement/proxy/upload_file',
                            fieldName: 'files',
                            headers: {},  // 移除原有的 API key，因为代理接口会处理
                            maxFileSize: 10 * 1024 * 1024, // 设置最大文件大小为10MB
                            customInsert(res, insertFn) {
                                // 处理上传成功的响应
                                if (res.success && res.files && res.files.length > 0) {
                                    const file = res.files[0];
                                    insertFn(file.url, file.original_name, file.url);
                                } else {
                                    ElementPlus.ElMessage.error('图片上传失败');
                                }
                            },
                            onError(file, err, res) {
                                console.error('图片上传错误', file, err, res);
                                ElementPlus.ElMessage.error('图片上传错误: ' + (res?.message || err?.message || '未知错误'));
                            }
                        },
                        // 添加视频上传配置
                        uploadVideo: {
                            server: '/projectmanagement/proxy/upload_file',
                            fieldName: 'files',
                            headers: {},
                            maxFileSize: 500 * 1024 * 1024, // 设置最大文件大小为50MB
                            allowedFileTypes: ['video/*'], // 允许的文件类型
                            customInsert(res, insertFn) {
                                // 处理上传成功的响应
                                if (res.success && res.files && res.files.length > 0) {
                                    const file = res.files[0];
                                    insertFn(file.url);
                                } else {
                                    ElementPlus.ElMessage.error('视频上传失败');
                                }
                            },
                            onError(file, err, res) {
                                console.error('视频上传错误', file, err, res);
                                ElementPlus.ElMessage.error('视频上传错误: ' + (res?.message || err?.message || '未知错误'));
                            }
                        }
                    }
                };
                
                // 创建编辑器
                window.taskEditor = createEditor({
                    selector: '#richEditorContent',
                    html: '',
                    config: editorConfig,
                    mode: 'default',
                });
                
                // 创建工具栏
                window.taskToolbar = createToolbar({
                    editor: window.taskEditor,
                    selector: '#richEditorToolbar',
                    mode: 'default',
                    config: {
                        toolbarKeys: [
                            'bold',
                            'italic',
                            'underline',
                            'uploadImage',
                            'uploadVideo'  // 添加视频上传按钮
                        ]
                    }
                });
                
                // 为编辑器添加自动焦点（移动端除外，避免键盘弹出遮挡内容）
                setTimeout(() => {
                    if (window.taskEditor && window.innerWidth > 1023) {
                        window.taskEditor.focus();
                    }
                }, 100);
                
            } catch (error) {
                console.error('初始化富文本编辑器失败:', error);
                ElementPlus.ElMessage.error('初始化编辑器失败，请刷新页面重试');
            }
        }, 200); // 增加延迟，确保模态框DOM已完全渲染
    };
    
    // 执行完成/取消完成任务的API请求
    const executeCompleteTask = (task, comment) => {
        const action = task.completed_date ? '取消完成' : '完成';
        
        fetch(`/projectmanagement/complete_task/${task.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                comment: comment,
                is_html: true // 添加标记表明内容是HTML格式
            })
        }).then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || '操作失败');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 更新任务状态
                const index = tasks.value.findIndex(t => t.id === task.id);
                if (index !== -1) {
                    tasks.value[index].completed_date = data.completed ? data.completed_date : null;
                }
                
                // 如果抽屉中显示的是当前任务，也更新抽屉中的任务状态
                if (drawer.value.task && drawer.value.task.id === task.id) {
                    drawer.value.task.completed_date = data.completed ? data.completed_date : null;
                    
                    // 添加新评论到任务日志中
                    if (drawer.value.task.logs) {
                        drawer.value.task.logs.unshift({
                            sender: '您',
                            time: new Date().toISOString(),
                            username: '您',
                            message: action === '取消完成' ? 
                                '取消完成任务' : 
                                comment, // 这里直接使用HTML内容
                            is_html: true // 标记为HTML内容
                        });
                    }
                }
                
                // 显示操作成功消息
                ElementPlus.ElMessage({
                    message: data.message,
                    type: 'success'
                });

                // 如果当前是在首页，刷新首页数据
                if (currentView.value === 'home') {
                    window.vueApp.loadHomeData();
                }
            } else {
                throw new Error(data.message || '操作失败');
            }
        })
        .catch(error => {
            ElementPlus.ElMessage({
                message: error.message,
                type: 'error'
            });
        });
    };

    // 跳转到项目
    const goToProject = (projectId) => {
        // 阻止事件冒泡，避免触发卡片的点击事件
        event?.stopPropagation();
        
        // 如果是当前项目，直接切换到项目视图
        if (currentProject.value && currentProject.value.id === projectId) {
            currentView.value = 'project';
            
            // 更新URL
            const url = new URL(window.location.href);
            url.searchParams.set('view_type', 'project');
            window.history.pushState({}, document.title, url);
            
            return;
        }
        
        // 否则跳转到该项目页面
        window.location.href = `/projectmanagement/main?project_id=${projectId}&view_type=project`;
    };

    // 删除任务
    const deleteTask = () => {
        // 检查是否有选中的任务
        if (!drawer.value.taskId) {
            ElementPlus.ElMessage({
                message: '请先选择一个任务',
                type: 'warning'
            });
            return;
        }

        // 弹出确认对话框
        ElementPlus.ElMessageBox.confirm(
            '确定要删除该任务吗？此操作不可逆！',
            '删除任务',
            {
                confirmButtonText: '确认删除',
                cancelButtonText: '取消',
                type: 'warning'
            }
        ).then(() => {
            // 用户确认删除，发送删除请求
            fetch(`/projectmanagement/api/delete_task/${drawer.value.taskId}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '删除任务失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 删除成功，更新任务列表
                    const index = tasks.value.findIndex(t => t.id === drawer.value.taskId);
                    if (index !== -1) {
                        tasks.value.splice(index, 1);
                    }
                    
                    // 关闭任务详情抽屉
                    closeDrawer();
                    
                    // 显示成功消息
                    ElementPlus.ElMessage({
                        message: data.message || '任务已成功删除',
                        type: 'success'
                    });
                } else {
                    throw new Error(data.message || '删除任务失败');
                }
            })
            .catch(error => {
                ElementPlus.ElMessage({
                    message: error.message,
                    type: 'error'
                });
            });
        }).catch(() => {
            // 用户取消删除，不做任何操作
        });
    };

    // 获取空数据文本
    const getEmptyText = () => {
        if (currentView.value === 'search') {
            return searchQuery.value ? '没有找到匹配的任务' : '请输入搜索关键词';
        } else if (currentView.value === 'completed') {
            return '今天还没有完成的任务';
        } else if (currentView.value === 'created') {
            return '您还没有发起任何任务';
        } else if (currentView.value === 'received') {
            return '您没有收到任何任务';
        } else {
            return '当前项目没有任务';
        }
    };

    // 获取列数
    const getColumnSpan = () => {
        return ['search', 'completed', 'created', 'received'].includes(currentView.value) ? 6 : 5;
    };

    // 选择项目
    const selectProject = (project) => {

        // 设置当前项目
        currentProject.value = project;
        
        // 更新URL，不刷新页面
        const url = new URL(window.location.href);
        url.searchParams.set('project_id', project.id);
        url.searchParams.set('view_type', 'project');
        window.history.pushState({}, document.title, url);
        
        // 修改当前视图为project，这将清除任何活动的菜单项
        currentView.value = 'project';
        
        // 显示加载状态
        tableLoading.value = true;
        tasks.value = [];
        // totalTasks.value = 0;

        // 使用AJAX获取项目任务数据
        fetch(`/projectmanagement/api/project_tasks?project_id=${project.id}`)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '获取项目任务失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                tableLoading.value = false;
                if (data.success) {
                    tasks.value = data.tasks || [];
                    // 更新总任务数量 - 后端分页
                    if (data.pagination && typeof data.pagination.total !== 'undefined') {
                        totalTasks.value = data.pagination.total;
                    } else {
                        // 兼容旧接口，如果没有返回分页信息，使用任务数组长度
                        totalTasks.value = tasks.value.length;
                    }


                    // 重置分页到第一页
                    // if (typeof currentPage !== 'undefined') {
                    //     currentPage.value = 1;
                    // }
                    
                    // 清除任务抽屉
                    if (drawer.value.visible) {
                        closeDrawer();
                    }
                } else {
                    throw new Error(data.message || '获取项目任务失败');
                }
            })
            .catch(error => {
                tableLoading.value = false;
                ElementPlus.ElMessage({
                    message: error.message,
                    type: 'error'
                });
            });
    };

    // 获取任务状态标签
    const getTaskStatusLabel = (task) => {
        // 没有截止日期的任务
        if (!task.deadline) {
            return {
                type: 'info',
                label: '无截止日期'
            };
        }

        // 获取今天的日期（YYYY-MM-DD格式）
        const today = new Date().toISOString().slice(0, 10);
        
        // 如果任务已完成
        if (task.completed_date) {
            // 比较完成日期和截止日期
            if (task.completed_date > task.deadline) {
                // 计算逾期天数
                const deadlineDate = new Date(task.deadline);
                const completedDate = new Date(task.completed_date);
                const diffTime = Math.abs(completedDate - deadlineDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                const overdueDaysDiv3 = Math.floor(diffDays / 3);
                
                return {
                    type: 'warning',
                    label: `逾期完成-(${overdueDaysDiv3})`
                };
            } else {
                return {
                    type: 'success',
                    label: '按时完成'
                };
            }
        } else {
            // 任务未完成，判断是否已逾期
            if (task.deadline < today) {
                // 计算逾期天数
                const deadlineDate = new Date(task.deadline);
                const todayDate = new Date(today);
                const diffTime = Math.abs(todayDate - deadlineDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                const overdueDaysDiv3 = Math.floor(diffDays / 3);
                
                return {
                    type: 'danger',
                    label: `逾期未完成-(${overdueDaysDiv3})`
                };
            } else if (task.deadline === today) {
                return {
                    type: 'warning',
                    label: '今日到期'
                };
            } else {
                return {
                    type: 'info',
                    label: '进行中'
                };
            }
        }
    };

    return {
        searchTasks,
        getRowClass,
        getCompleteTitle,
        initReplyEditor,
        handleRowClick,
        closeDrawer,
        sendReply,
        completeTask,
        goToProject,
        deleteTask,
        getEmptyText,
        getColumnSpan,
        selectProject,
        getTaskStatusLabel,
        sendDingReply,
    };
} 