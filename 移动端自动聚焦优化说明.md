# 移动端自动聚焦优化说明

## 问题描述
用户反馈移动端任务管理点击抽屉栏进入详情页面时，输入框会自动获得焦点，导致手机键盘弹出并遮挡内容，影响用户体验。

## 问题分析
在多个JavaScript文件中，富文本编辑器初始化时都有自动聚焦的逻辑：
```javascript
setTimeout(() => {
    if (window.replyEditor) {
        window.replyEditor.focus(); // 这会在移动端触发键盘弹出
    }
}, 100);
```

这种自动聚焦在桌面端是好的用户体验，但在移动端会导致：
1. 键盘自动弹出遮挡内容
2. 用户无法看到完整的任务详情
3. 需要手动关闭键盘才能正常浏览

## 解决方案
在所有自动聚焦的代码中添加移动端检测，只在桌面端（屏幕宽度 > 1023px）时才自动聚焦：

```javascript
// 为编辑器添加自动焦点（移动端除外，避免键盘弹出遮挡内容）
setTimeout(() => {
    if (window.replyEditor && window.innerWidth > 1023) {
        window.replyEditor.focus();
    }
}, 100);
```

## 修改文件列表

### 1. static/js/pm_tasks.js
**修改位置**: 第215-220行 和 第604-609行
**修改内容**: 
- 回复编辑器自动聚焦逻辑
- 任务编辑器自动聚焦逻辑
- 添加移动端检测条件 `window.innerWidth > 1023`

### 2. static/js/pm_task_handler.js  
**修改位置**: 第134-139行
**修改内容**:
- 任务编辑器自动聚焦逻辑
- 添加移动端检测条件

### 3. static/js/pm_templates.js
**修改位置**: 第354-359行
**修改内容**:
- 模板任务编辑器自动聚焦逻辑
- 添加移动端检测条件

## 技术实现细节

### 移动端检测标准
- **桌面端**: `window.innerWidth > 1023` - 允许自动聚焦
- **移动端**: `window.innerWidth <= 1023` - 禁用自动聚焦

### 检测时机
在编辑器初始化完成后的延时回调中进行检测，确保：
1. 编辑器已完全初始化
2. 窗口尺寸检测准确
3. 不影响桌面端的用户体验

### 兼容性考虑
- 保持桌面端原有的自动聚焦体验
- 移动端用户可以手动点击输入框来聚焦
- 响应式设计，窗口大小变化时行为一致

## 预期效果

### ✅ 移动端优化
- 抽屉打开时不会自动弹出键盘
- 用户可以完整查看任务详情
- 需要输入时可以手动点击输入框

### ✅ 桌面端保持
- 保持原有的自动聚焦体验
- 提高输入效率
- 用户体验不受影响

### ✅ 响应式适配
- 根据屏幕尺寸动态调整行为
- 支持设备旋转等场景
- 统一的判断标准

## 测试建议

### 移动端测试
1. 在手机浏览器中打开任务管理页面
2. 点击任务卡片进入抽屉详情页
3. 确认键盘不会自动弹出
4. 手动点击输入框确认可以正常聚焦

### 桌面端测试  
1. 在桌面浏览器中打开任务管理页面
2. 点击任务进入编辑模式
3. 确认输入框自动获得焦点
4. 确认可以直接开始输入

### 响应式测试
1. 调整浏览器窗口大小
2. 在1023px临界点附近测试
3. 确认行为切换正确

## 相关文件
- `static/js/pm_tasks.js` - 主要任务管理脚本
- `static/js/pm_task_handler.js` - 任务处理器
- `static/js/pm_templates.js` - 模板管理脚本
- `移动端省略号优化与卡片居中说明.md` - 相关移动端优化文档
