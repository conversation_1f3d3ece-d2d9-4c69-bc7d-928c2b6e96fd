// Template Management Logic

/**
 * Initializes the template management features.
 * @param {Object} Vue Vue instance (specifically { ref, computed, onMounted })
 * @param {Object} ElementPlus ElementPlus instance for UI components
 * @param {ref} currentViewRef Reference to the main app's currentView state
 * @returns {Object} Reactive state and functions for template management
 */
export function initializeTemplateManagement(Vue, ElementPlus, currentViewRef) {
    const { ref, computed, onMounted } = Vue;

    // Reactive state for templates
    const templates = ref([]);
    const templateLoading = ref(false);
    const templateSearch = ref(''); // Optional: Add search later if needed

    // New state for template details view
    const currentTemplateDetails = ref(null); // Stores basic info of the viewed template
    const templateTasks = ref([]); // Stores tasks of the viewed template
    const templateTasksLoading = ref(false); // Loading state for template tasks

    // New state for selected template task
    const selectedTemplateTask = ref(null);

    // New state for the template task dialog
    const templateTaskDialog = ref({
        visible: false,
        title: '任务',
        loading: false,
        isEditMode: false,
        form: {},
        rules: {
            name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
            urgency: [{ required: true, message: '请选择紧急程度', trigger: 'change' }],
            // Relative deadline is optional
        },
        parentTask: null, // Store parent task for adding subtasks
        urgencyOptions: [
            { value: '一般', label: '一般' },
            { value: '紧急', label: '紧急' },
        ],
    });

    // 新增状态和功能
    const templateEditDialog = ref({
        visible: false,
        loading: false,
        form: {
            id: null,
            name: '',
            description: ''
        },
        rules: {
            name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }]
        }
    });

    // 新增模板对话框状态
    const addTemplateDialog = ref({
        visible: false,
        loading: false,
        form: {
            name: '',
            description: ''
        },
        rules: {
            name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }]
        }
    });

    // Placeholder function to fetch templates (replace with actual API call)
    const loadTemplates = async () => {
        templateLoading.value = true;
        console.log("Fetching templates from API...");
        try {
            // Fetch data from the backend API
            const response = await fetch('/projectmanagement/api/templates');
            
            if (!response.ok) {
                // Try to parse error message from response body
                let errorData = { message: `HTTP error! status: ${response.status}` };
                try {
                    errorData = await response.json();
                } catch (e) { /* Ignore parsing error */ }
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                templates.value = data.templates || [];
                console.log("Templates loaded from API:", templates.value);
            } else {
                throw new Error(data.message || '获取模板列表失败');
            }

        } catch (error) {
            console.error("Failed to load templates from API:", error);
            ElementPlus.ElMessage({ type: 'error', message: error.message || '加载模板列表失败' });
            templates.value = []; // Clear templates on error
        } finally {
            templateLoading.value = false;
        }
    };

    // New function to fetch tasks for a specific template
    const loadTemplateTasks = async (templateId) => {
        templateTasksLoading.value = true;
        templateTasks.value = []; // Clear previous tasks
        currentTemplateDetails.value = null; // Clear previous details
        selectedTemplateTask.value = null; // Clear selected task
        console.log(`Fetching tasks for template ID: ${templateId}...`);
        try {
            const response = await fetch(`/projectmanagement/api/template/${templateId}`);
            if (!response.ok) {
                let errorData = { message: `HTTP error! status: ${response.status}` };
                try { errorData = await response.json(); } catch (e) { /* Ignore */ }
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            if (data.success) {
                currentTemplateDetails.value = data.template;
                templateTasks.value = data.tasks || [];
                console.log("Template details loaded:", currentTemplateDetails.value);
                console.log("Template tasks loaded:", templateTasks.value);
            } else {
                throw new Error(data.message || '获取模板任务失败');
            }
        } catch (error) {
            console.error("Failed to load template tasks:", error);
            ElementPlus.ElMessage({ type: 'error', message: error.message || '加载模板任务失败' });
            currentTemplateDetails.value = { name: '加载失败' }; // Show error state
            templateTasks.value = [];
        } finally {
            templateTasksLoading.value = false;
        }
    };

    // Computed property to filter templates (if search is added)
    const filteredTemplates = computed(() => {
        if (!templateSearch.value) {
            return templates.value;
        }
        const search = templateSearch.value.toLowerCase();
        return templates.value.filter(template => 
            template.name.toLowerCase().includes(search) ||
            (template.description && template.description.toLowerCase().includes(search))
        );
    });

    // 显示新增模板对话框
    const showAddTemplateDialog = () => {
        console.log("Triggered showAddTemplateDialog");
        // 重置表单
        addTemplateDialog.value.form = {
            name: '',
            description: ''
        };
        // 显示对话框
        addTemplateDialog.value.visible = true;
    };

    // 关闭新增模板对话框
    const closeAddTemplateDialog = () => {
        addTemplateDialog.value.visible = false;
    };

    // 提交新增模板
    const submitAddTemplate = async () => {
        // 表单校验
        if (!addTemplateDialog.value.form.name.trim()) {
            ElementPlus.ElMessage({ type: 'warning', message: '请输入模板名称' });
            return;
        }

        addTemplateDialog.value.loading = true;

        try {
            const response = await fetch('/projectmanagement/api/template', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: addTemplateDialog.value.form.name.trim(),
                    description: addTemplateDialog.value.form.description.trim()
                })
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result.message || '创建模板失败');
            }

            // 添加新模板到列表
            if (result.template) {
                templates.value.unshift(result.template);
            }

            ElementPlus.ElMessage({
                type: 'success',
                message: result.message || '模板创建成功'
            });

            closeAddTemplateDialog();

        } catch (error) {
            console.error('创建模板失败:', error);
            ElementPlus.ElMessage({
                type: 'error',
                message: error.message || '创建模板失败'
            });
        } finally {
            addTemplateDialog.value.loading = false;
        }
    };

    // Modified function to handle clicking on a template card
    const viewTemplate = (template) => {
        console.log("Viewing template details for:", template);
        if (currentViewRef) {
            currentViewRef.value = 'template_details'; // Change the main view state
            // Immediately trigger loading the tasks for this template
            loadTemplateTasks(template.id);
        } else {
            console.error("currentViewRef is not available in initializeTemplateManagement");
            ElementPlus.ElMessage({ type: 'error', message: '无法切换视图' });
        }
        // Old message logic removed
        // ElementPlus.ElMessage(`查看模板 ${template.name} 功能待实现`);
    };

    // Handle clicking a row in the template tasks table
    const handleTemplateTaskRowClick = (row) => {
        selectedTemplateTask.value = row;
        console.log("Selected template task:", row);
    };

    // --- Template Task CRUD Dialog Functions ---

    // 初始化模板任务富文本编辑器
    const initTemplateTaskEditor = (initialContent = '') => {
        // 确保之前的编辑器实例被销毁
        if (window.templateTaskEditor) {
            window.templateTaskEditor.destroy();
            window.templateTaskEditor = null;
        }
        if (window.templateTaskToolbar) {
            window.templateTaskToolbar.destroy();
            window.templateTaskToolbar = null;
        }
        
        // 确保DOM元素已经渲染
        const richEditorContent = document.getElementById('templateTaskEditorContent');
        const richEditorToolbar = document.getElementById('templateTaskEditorToolbar');
        
        if (!richEditorContent || !richEditorToolbar) {
            console.error('找不到模板任务编辑器DOM元素', {
                content: !!richEditorContent,
                toolbar: !!richEditorToolbar
            });
            return;
        }
        
        try {
            if (typeof window.wangEditor === 'undefined') {
                console.error('编辑器组件未加载');
                return;
            }
            
            const { createEditor, createToolbar } = window.wangEditor;
            
            // 配置编辑器
            const editorConfig = {
                placeholder: '请输入任务描述...',
                onChange: (editor) => {
                    // 更新编辑器内容到Vue的数据模型
                    templateTaskDialog.value.form.description = editor.getHtml();
                },
                MENU_CONF: {
                    uploadImage: {
                        server: '/projectmanagement/proxy/upload_file',
                        fieldName: 'files',
                        headers: {},
                        maxFileSize: 10 * 1024 * 1024, // 设置最大文件大小为10MB
                        customInsert(res, insertFn) {
                            // 处理上传成功的响应
                            if (res.success && res.files && res.files.length > 0) {
                                const file = res.files[0];
                                insertFn(file.url, file.original_name, file.url);
                            } else {
                                ElementPlus.ElMessage.error('图片上传失败');
                            }
                        },
                        onError(file, err, res) {
                            console.error('图片上传错误', file, err, res);
                            ElementPlus.ElMessage.error('图片上传错误: ' + (res?.message || err?.message || '未知错误'));
                        }
                    },
                    // 添加视频上传配置
                    uploadVideo: {
                        server: '/projectmanagement/proxy/upload_file',
                        fieldName: 'files',
                        headers: {},
                        maxFileSize: 50 * 1024 * 1024, // 设置最大文件大小为50MB
                        allowedFileTypes: ['video/*'], // 允许的文件类型
                        customInsert(res, insertFn) {
                            // 处理上传成功的响应
                            if (res.success && res.files && res.files.length > 0) {
                                const file = res.files[0];
                                insertFn(file.url);
                            } else {
                                ElementPlus.ElMessage.error('视频上传失败');
                            }
                        },
                        onError(file, err, res) {
                            console.error('视频上传错误', file, err, res);
                            ElementPlus.ElMessage.error('视频上传错误: ' + (res?.message || err?.message || '未知错误'));
                        }
                    }
                }
            };
            
            // 创建编辑器
            window.templateTaskEditor = createEditor({
                selector: '#templateTaskEditorContent',
                html: initialContent,
                config: editorConfig,
                mode: 'default',
            });
            
            // 创建工具栏
            window.templateTaskToolbar = createToolbar({
                editor: window.templateTaskEditor,
                selector: '#templateTaskEditorToolbar',
                mode: 'default',
                config: {
                    toolbarKeys: [
                        'bold',
                        'italic',
                        'underline',
                        'uploadImage',
                        'uploadVideo',  // 添加视频上传按钮
                        'justifyLeft',
                        'justifyCenter',
                        'justifyRight',

                        'undo',
                        'redo'
                    ]
                }
            });
            
            // 为编辑器添加自动焦点（移动端除外，避免键盘弹出遮挡内容）
            setTimeout(() => {
                if (window.templateTaskEditor && window.innerWidth > 1023) {
                    window.templateTaskEditor.focus();
                }
            }, 100);
        } catch (error) {
            console.error('初始化模板任务富文本编辑器失败:', error);
            ElementPlus.ElMessage.error('初始化编辑器失败，请刷新页面重试');
        }
    };

    const resetTemplateTaskDialog = () => {
        // 销毁编辑器实例
        if (window.templateTaskEditor) {
            window.templateTaskEditor.destroy();
            window.templateTaskEditor = null;
        }
        if (window.templateTaskToolbar) {
            window.templateTaskToolbar.destroy();
            window.templateTaskToolbar = null;
        }
        
        templateTaskDialog.value = {
            ...templateTaskDialog.value,
            visible: false,
            title: '任务',
            loading: false,
            isEditMode: false,
            form: {
                id: null,
                name: '',
                description: '',
                urgency: '一般', // Default value
                relative_deadline_days: null,
                assignees: '', // Simple text field for roles/descriptions
                parent_id: null
            },
            parentTask: null
        };
        // If there's a form ref associated, reset validation (assuming a ref named templateTaskFormRef)
        // You'll need to pass this ref if you use form validation in the template
        // if (templateTaskFormRef && templateTaskFormRef.value) {
        //     templateTaskFormRef.value.resetFields();
        // }
    };

    const showAddTemplateTaskDialog = () => {
        resetTemplateTaskDialog();
        templateTaskDialog.value.title = '新增模板任务';
        templateTaskDialog.value.visible = true;
        
        // 在DOM更新后初始化富文本编辑器
        setTimeout(() => {
            initTemplateTaskEditor('');
        }, 100);
    };

    const showAddTemplateSubTaskDialog = () => {
        if (!selectedTemplateTask.value || selectedTemplateTask.value.is_child) return;
        resetTemplateTaskDialog();
        templateTaskDialog.value.title = `为 "${selectedTemplateTask.value.name}" 添加子任务`;
        templateTaskDialog.value.parentTask = selectedTemplateTask.value;
        templateTaskDialog.value.form.parent_id = selectedTemplateTask.value.id;
        templateTaskDialog.value.visible = true;
        
        // 在DOM更新后初始化富文本编辑器
        setTimeout(() => {
            initTemplateTaskEditor('');
        }, 100);
    };

    const showEditTemplateTaskDialog = () => {
        if (!selectedTemplateTask.value) return;
        resetTemplateTaskDialog();
        templateTaskDialog.value.title = `编辑模板任务: ${selectedTemplateTask.value.name}`;
        templateTaskDialog.value.isEditMode = true;
        templateTaskDialog.value.form = { 
            ...selectedTemplateTask.value, 
            relative_deadline_days: selectedTemplateTask.value.relative_deadline_days || null // Ensure null if undefined
        }; // Copy existing data
        templateTaskDialog.value.visible = true;
        
        // 在DOM更新后初始化富文本编辑器，并设置初始内容
        setTimeout(() => {
            initTemplateTaskEditor(selectedTemplateTask.value.description || '');
        }, 100);
    };

    const closeTemplateTaskDialog = () => {
        // 销毁编辑器实例
        if (window.templateTaskEditor) {
            window.templateTaskEditor.destroy();
            window.templateTaskEditor = null;
        }
        if (window.templateTaskToolbar) {
            window.templateTaskToolbar.destroy();
            window.templateTaskToolbar = null;
        }
        
        resetTemplateTaskDialog();
    };

    const submitTemplateTask = async () => {
        // Add form validation logic here if using ElementPlus Form rules
        // e.g., templateTaskFormRef.value.validate(async (valid) => { ... });
        
        // 从富文本编辑器获取最新内容
        if (window.templateTaskEditor) {
            templateTaskDialog.value.form.description = window.templateTaskEditor.getHtml();
        }
        
        const isEdit = templateTaskDialog.value.isEditMode;
        const taskData = { ...templateTaskDialog.value.form };
        const templateId = currentTemplateDetails.value?.id;
        const taskId = taskData.id;

        if (!templateId && !isEdit) {
            ElementPlus.ElMessage({ type: 'error', message: '无法确定当前模板ID' });
            return;
        }

        let url = '';
        let method = '';

        if (isEdit) {
            url = `/projectmanagement/api/template/task/${taskId}`;
            method = 'PUT';
        } else {
            url = `/projectmanagement/api/template/${templateId}/task`;
            method = 'POST';
        }

        templateTaskDialog.value.loading = true;

        try {
            const response = await fetch(url, {
                method: method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(taskData)
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result.message || (isEdit ? '更新任务失败' : '添加任务失败'));
            }

            ElementPlus.ElMessage({ type: 'success', message: result.message });

            // Update the local task list
            if (isEdit) {
                const index = templateTasks.value.findIndex(t => t.id === taskId);
                if (index !== -1) {
                    templateTasks.value[index] = { ...templateTasks.value[index], ...result.task };
                }
            } else {
                // For new tasks, ideally, re-fetch or smartly insert based on parent_id
                // Simple approach: re-fetch all tasks for simplicity
                await loadTemplateTasks(templateId);
            }

            closeTemplateTaskDialog();

        } catch (error) {
            console.error("Error submitting template task:", error);
            ElementPlus.ElMessage({ type: 'error', message: error.message });
        } finally {
            templateTaskDialog.value.loading = false;
        }
    };

    const deleteTemplateTask = async () => {
        if (!selectedTemplateTask.value) return;

        const taskToDelete = selectedTemplateTask.value;

        try {
            await ElementPlus.ElMessageBox.confirm(
                `确定要删除任务 "${taskToDelete.name}" 吗？此操作不可恢复。`,
                '确认删除',
                { type: 'warning', confirmButtonText: '删除', cancelButtonText: '取消' }
            );

            // Proceed with deletion
            const loading = ElementPlus.ElLoading.service({ text: '正在删除...' });
            try {
                const response = await fetch(`/projectmanagement/api/template/task/${taskToDelete.id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (!response.ok || !result.success) {
                    throw new Error(result.message || '删除任务失败');
                }

                ElementPlus.ElMessage({ type: 'success', message: result.message });

                // Remove task from local list
                templateTasks.value = templateTasks.value.filter(t => t.id !== taskToDelete.id);
                selectedTemplateTask.value = null; // Deselect

            } finally {
                loading.close();
            }
        } catch (error) {
            // Handle cancellation or API error
            if (error !== 'cancel') {
                console.error("Error deleting template task:", error);
                ElementPlus.ElMessage({ type: 'error', message: error.message });
            } else {
                ElementPlus.ElMessage({ type: 'info', message: '已取消删除' });
            }
        }
    };

    // 删除模板
    const deleteTemplate = async (template) => {
        try {
            await ElementPlus.ElMessageBox.confirm(
                `确定要删除模板 "${template.name}" 吗？此操作不可恢复。`,
                '删除确认',
                {
                    confirmButtonText: '删除',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            );
            
            const loading = ElementPlus.ElLoading.service({ text: '正在删除...' });
            
            try {
                const response = await fetch(`/projectmanagement/api/template/${template.id}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (!response.ok || !result.success) {
                    throw new Error(result.message || '删除模板失败');
                }
                
                // 从列表中移除该模板
                const index = templates.value.findIndex(t => t.id === template.id);
                if (index !== -1) {
                    templates.value.splice(index, 1);
                }
                
                ElementPlus.ElMessage({
                    type: 'success',
                    message: result.message || '模板已成功删除'
                });
                
            } finally {
                loading.close();
            }
            
        } catch (error) {
            if (error !== 'cancel') {
                console.error('删除模板失败:', error);
                ElementPlus.ElMessage({
                    type: 'error',
                    message: error.message || '删除模板失败'
                });
            }
        }
    };

    // 新增功能
    const showEditTemplateDialog = (template) => {
        templateEditDialog.value.form = {
            id: template.id,
            name: template.name,
            description: template.description || ''
        };
        templateEditDialog.value.visible = true;
    };

    const closeTemplateEditDialog = () => {
        templateEditDialog.value.visible = false;
        templateEditDialog.value.form = {
            id: null,
            name: '',
            description: ''
        };
    };

    const submitTemplateEdit = async () => {
        const form = templateEditDialog.value.form;
        if (!form.name.trim()) {
            ElementPlus.ElMessage({ type: 'warning', message: '请输入模板名称' });
            return;
        }

        templateEditDialog.value.loading = true;

        try {
            const response = await fetch(`/projectmanagement/api/template/${form.id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: form.name.trim(),
                    description: form.description.trim()
                })
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result.message || '更新模板信息失败');
            }

            // 更新本地模板列表中的数据
            const index = templates.value.findIndex(t => t.id === form.id);
            if (index !== -1) {
                templates.value[index] = { ...templates.value[index], ...result.template };
            }

            ElementPlus.ElMessage({
                type: 'success',
                message: result.message || '模板信息已更新'
            });

            closeTemplateEditDialog();

        } catch (error) {
            console.error('更新模板信息失败:', error);
            ElementPlus.ElMessage({
                type: 'error',
                message: error.message || '更新模板信息失败'
            });
        } finally {
            templateEditDialog.value.loading = false;
        }
    };

    // --- New function to move template tasks ---
    const moveTemplateTask = async (task, direction) => {
        if (!task || !currentTemplateDetails.value?.id) {
            console.error("Cannot move task: Missing task data or template context.");
            return;
        }

        const loading = ElementPlus.ElLoading.service({ 
            target: '.template-management-grid', // Target the specific container if possible
            text: '正在调整顺序...'
        });

        try {
            const response = await fetch('/projectmanagement/api/template/task/reorder', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ task_id: task.id, direction: direction })
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
                // Handle specific message for already top/bottom
                if (result.message === '任务已在顶部或底部') {
                     ElementPlus.ElMessage({ type: 'info', message: result.message });
                } else {
                    throw new Error(result.message || '调整任务顺序失败');
                }
            }

            // Reload tasks to reflect the new order
            // Optimistic update could be done here, but reloading is simpler for now
            await loadTemplateTasks(currentTemplateDetails.value.id);
            
            // Don't show success message if it was already top/bottom
            if (result.message !== '任务已在顶部或底部') {
                 ElementPlus.ElMessage({ type: 'success', message: result.message || '顺序已更新' });
            }

        } catch (error) {
            console.error("Error moving template task:", error);
            ElementPlus.ElMessage({ type: 'error', message: error.message });
        } finally {
            loading.close();
        }
    };

    // Load templates when the component is mounted
    onMounted(() => {
        // We only load templates if the current view is 'templates'
        // This check might need refinement depending on how views are switched
        // Consider loading them when the 'templates' view becomes active
        loadTemplates(); // Let's load them when the view is activated instead
    });

    // Expose state and functions
    return {
        templates, // Use 'filteredTemplates' in the template if search is active
        templateLoading,
        templateSearch,
        filteredTemplates, // Use this in the v-for for display
        loadTemplates,      // Function to potentially reload templates
        showAddTemplateDialog, // Function to open the add dialog
        viewTemplate,        // Function to handle clicking on a template card
        deleteTemplate,      // Function to handle template deletion
        // New exports for details view
        currentTemplateDetails,
        templateTasks,
        templateTasksLoading,
        loadTemplateTasks,
        // Task CRUD
        selectedTemplateTask,
        templateTaskDialog,
        handleTemplateTaskRowClick,
        showAddTemplateTaskDialog,
        showAddTemplateSubTaskDialog,
        showEditTemplateTaskDialog,
        closeTemplateTaskDialog,
        submitTemplateTask,
        deleteTemplateTask,
        // Template Edit
        templateEditDialog,
        showEditTemplateDialog,
        closeTemplateEditDialog,
        submitTemplateEdit,
        // Export the new move function
        moveTemplateTask,
        // Add Template Dialog
        addTemplateDialog,
        closeAddTemplateDialog,
        submitAddTemplate
    };
} 